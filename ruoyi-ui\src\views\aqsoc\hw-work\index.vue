<template>
  <div class="custom-container">
    <div v-show="!formVisible1 && !makePlanShow" class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-row :gutter="16">
          <el-form @submit.native.prevent>
            <el-col :span="6">
              <el-form-item label="年度">
                <el-input v-model="query.year" placeholder="请输入年度" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="技术支撑单位">
                <el-input v-model="query.supportOrgs" placeholder="请输入技术支撑单位" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="custom-search-btn">
                <el-button class="btn1" size="small" @click="search()">查询</el-button>
                <el-button class="btn2" size="small" @click="reset()">重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </div>

      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">HW事务列表</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  @click="addOrUpdateHandle()"
                >新增
                </el-button >
              </el-col>
            </el-row>
          </div>
        </div>
        <el-table height="100%" v-loading="listLoading" :data="list" @sort-change='sortChange'
                  :span-method="arraySpanMethod">
          <el-table-column
            prop="year"
            label="年度" align="left">
          </el-table-column>
          <el-table-column
            prop="supportOrgs"
            label="技术支撑单位" align="left">
          </el-table-column>
          <el-table-column
            prop="userNames"
            label="联络人" align="left">
          </el-table-column>
          <el-table-column
            prop="hwStart"
            label="HW开始时间" align="left">
          </el-table-column>
          <el-table-column
            prop="hwEnd"
            label="HW结束时间" align="left">
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间" align="left">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="makePlan(scope.row)">制定HW计划</el-button>
              <el-button type="text" @click="updateHandle(scope.row, true)">HW看板</el-button>
              <el-button type="text" @click="copy(scope.row)">复制</el-button>
              <el-button type="text" class="table-delBtn" @click="handleDel(scope.row.id)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination :total="total" :page.sync="listQuery.currentPage" :limit.sync="listQuery.pageSize"
                    @pagination="initData"></pagination>
      </div>
      <Form :visible.sync="formVisible" ref="addForm" @refresh="refresh"></Form>
    </div>
    <big-form :visible.sync="formVisible1" ref="BigForm" @refresh="refresh" ></big-form>
    <plan v-if="makePlanShow" :show.sync="makePlanShow" :work-info="curRow"></plan>
  </div>
</template>

<script>
import Form from './form'
import BigForm from './big-form'
import {mapGetters} from "vuex";
import {getList, delData} from '@/api/aqsoc/work-hw/crud'
import Plan from './plan'

export default {
  components: {Form, BigForm,Plan},
  props: {},
  data() {
    return {
      keyword: '',
      expandObj: {},
      query: {},
      list: [],
      listLoading: true,
      formVisible: false,
      formVisible1: false,
      total: 0,
      mergeList: [],
      listQuery: {
        currentPage: 1,
        pageSize: 20
      },
      makePlanShow: false,
      curRow: null,
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    menuId() {
      return this.$route.meta.modelId || ''
    }
  },
  created() {
    this.initSearchDataAndListData()
  },
  methods: {
    addOrUpdateHandle(id, isDetail, isAudit, isCopy) {
      this.formVisible = true
      this.$nextTick(() => {
        this.$refs.addForm.init(id, isDetail, isAudit,isCopy)
      })
    },
    updateHandle(row, isDetail, isAudit) {
      this.formVisible1 = true
      this.$nextTick(() => {
        this.$refs.BigForm.init(row, isDetail, isAudit)
      })
    },
    makePlan(row){
      this.curRow = row;
      this.makePlanShow = true;
    },
    copy(row){
      this.addOrUpdateHandle(row.id,true,true,true);
    },
    arraySpanMethod({column}) {
      for (let i = 0; i < this.mergeList.length; i++) {
        if (column.property == this.mergeList[i].prop) {
          return [this.mergeList[i].rowspan, this.mergeList[i].colspan]
        }
      }
    },
    sortChange({column, prop, order}) {
      this.initData()
    },
    async initSearchDataAndListData() {
      await this.initSearchData()
      this.initData()
    },
    //初始化查询的默认数据
    async initSearchData() {
    },
    initData() {
      this.listLoading = true;
      let _query = {
        ...this.listQuery,
        ...this.query,
        keyword: this.keyword,
        menuId: this.menuId
      };
      getList(_query).then(res => {
        var _list = [];
        for (let i = 0; i < res.data.list.length; i++) {
          let _data = res.data.list[i];
          _list.push(_data)
        }
        this.list = _list.map(o => ({
          ...o,
          ...this.expandObj,
        }))
        this.total = res.data.pagination.total
        this.listLoading = false
      })
    },
    search() {
      this.listQuery.currentPage = 1
      this.listQuery.pageSize = 20
      this.listQuery.sort = "desc"
      this.listQuery.sidx = ""
      this.initData()
    },
    refresh(isrRefresh) {
      this.formVisible = false
      this.formVisible1 = false
      if (isrRefresh) this.reset()
    },
    reset() {
      for (let key in this.query) {
        this.query[key] = undefined
      }
      this.search()
    },
    handleDel(id) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        type: 'warning'
      }).then(() => {
        delData(id).then(res => {
          this.$message({
            type: 'success',
            message: res.msg,
          });
          this.initData()
        })
      }).catch(() => {
      });
    },
  }
}
</script>
<style scoped lang="scss">

</style>
