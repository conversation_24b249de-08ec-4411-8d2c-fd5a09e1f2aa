{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\threaten\\AttackAlarm.js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\threaten\\AttackAlarm.js", "mtime": 1756294198385}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1751956515056}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listAttackAlarm", "query", "request", "url", "method", "params", "getAttackAlarm", "id", "getDipDetails", "getThreatenNameDetails", "handleVuln", "data", "handleBatchVuln", "groupAlarmLevelStatistics", "addAttackAlarm", "updateAttackAlarm", "delAttackAlarm"], "sources": ["E:/wsh/augment_workspace/aqsoc-main/ruoyi-ui/src/api/threaten/AttackAlarm.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询攻击者视角告警列列表\r\nexport function listAttackAlarm(query) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询攻击者视角告警列详细\r\nexport function getAttackAlarm(id) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询攻击目标IP列表\r\nexport function getDipDetails(query) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/getDipDetails',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询告警类型列表\r\nexport function getThreatenNameDetails(query) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/getThreatenNameDetails',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function handleVuln(data) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/handle',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function handleBatchVuln(data) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/handleBatch',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function groupAlarmLevelStatistics(query) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/groupAlarmLevelStatistics',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 新增攻击者视角告警列\r\nexport function addAttackAlarm(data) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改攻击者视角告警列\r\nexport function updateAttackAlarm(data) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除攻击者视角告警列\r\nexport function delAttackAlarm(id) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,EAAE,EAAE;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,EAAE;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACP,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,sBAAsBA,CAACR,KAAK,EAAE;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASS,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASE,yBAAyBA,CAACZ,KAAK,EAAE;EAC/C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iDAAiD;IACtDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,cAAcA,CAACH,IAAI,EAAE;EACnC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,iBAAiBA,CAACJ,IAAI,EAAE;EACtC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACT,EAAE,EAAE;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,EAAE;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}