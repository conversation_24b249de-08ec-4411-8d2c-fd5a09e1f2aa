package com.ruoyi;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.cron.CronUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.druid.util.JdbcUtils;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.ruoyi.datainterface.event.InterfaceEventMonitor;
import com.ruoyi.monitor2.changting.job.ScanResultMonitor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.*;


/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.ruoyi", "cn.anmte"}, exclude = {
        DataSourceAutoConfiguration.class
})
@EnableScheduling
@Slf4j
public class RuoYiApplication {
    public static void main(String[] args) throws UnknownHostException {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        ConfigurableApplicationContext application = SpringApplication.run(RuoYiApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        log.info("\n----------------------------------------------------------\n\t" +
                "Application is running! Access URLs:\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "\n\t" +
                "External: \thttp://" + ip + ":" + port + path + "\n\t" +
                "Swagger文档: \thttp://" + ip + ":" + port + path + "doc.html\n" +
                "----------------------------------------------------------");
        //hutool动态任务支持
        CronUtil.setMatchSecond(true);
        CronUtil.start();

        String maxActive = SpringUtil.getProperty("spring.datasource.druid.maxActive");
        if(StrUtil.isNotBlank(maxActive)){
            SqlSessionFactory sessionFactory = SpringUtil.getBean(SqlSessionFactory.class);
            SqlSession sqlSession = sessionFactory.openSession();
            Connection connection = sqlSession.getConnection();
            try {
                JdbcUtils.execute(connection,StrUtil.format("set global max_connections = {};", Integer.parseInt(maxActive)+20));
            } catch (SQLException ignored) {

            }finally {
                try {
                    connection.close();
                    sqlSession.close();
                } catch (SQLException ignored) {

                }
            }
        }
    }

    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(50);
        taskScheduler.setRemoveOnCancelPolicy(true);
        taskScheduler.setAwaitTerminationSeconds(60);
        return taskScheduler;
    }

    @Bean(name = "deductionDetailTaskExecutor")
    public ExecutorService deductionDetailTaskExecutor() {
        return ExecutorBuilder.create()
                .setCorePoolSize(5)
                .setMaxPoolSize(20)
                .setWorkQueue(new LinkedBlockingQueue<>(1000))
                .setHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

    @Bean(name = "syncAttackAlarmTaskExecutor")
    public ExecutorService syncAttackAlarmTaskExecutor() {
        return ExecutorBuilder.create()
                .setCorePoolSize(5)
                .setMaxPoolSize(20)
                .setWorkQueue(new LinkedBlockingQueue<>(1000))
                .setHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

    @Bean
    public void initXprocessEvent() {
        ScanResultMonitor resultMonitor = new ScanResultMonitor();
        resultMonitor.initXprocessEventByDB();
    }

    /*
    @Bean
    public void initFtp() {
        FtpUtils.init(new FtpConfig().getPool());
    }*/

    @Bean
    public void initDataInterEvent() {
        InterfaceEventMonitor.getInstance();
        //new EventMonitor().start();
    }

    /**
     * 添加分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL)); // 如果配置多个插件, 切记分页最后添加
        // 如果有多数据源可以不配具体类型, 否则都建议配上具体的 DbType
        return interceptor;
    }

    @Bean(name = "delayExecutorService")
    public ScheduledExecutorService delayExecutorService() {
        ThreadFactory threadFactory = ThreadFactoryBuilder.create().setNamePrefix("delay-task-").build();
        return Executors.newScheduledThreadPool(20, threadFactory);
    }

    @Bean(name = "delayTaskMap")
    public Map<String, ScheduledFuture<?>> delayTaskMap() {
        return new ConcurrentHashMap<>();
    }
}
