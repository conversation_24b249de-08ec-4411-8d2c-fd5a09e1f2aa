2025-08-27 18:36:39.889 [async-task-pool178] ERROR c.a.d.f.s.<PERSON>atFilter - [internalAfterStatementExecute,504] - slow sql 1075 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-27 18:36:29"]
2025-08-27 18:36:44.593 [async-task-pool179] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4625 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:36:11"]
2025-08-27 18:36:48.196 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7497 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:36:29"]
2025-08-27 18:36:50.525 [async-task-pool183] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11712 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:36:31"]
2025-08-27 18:36:57.534 [async-task-pool183] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7001 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:36:31"]
2025-08-27 18:37:36.791 [async-task-pool183] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 39253 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:36:31"]
2025-08-27 18:37:43.532 [async-task-pool61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4934 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:37:09"]
2025-08-27 18:37:46.893 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7694 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:37:33"]
2025-08-27 18:37:49.009 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10958 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:37:31"]
2025-08-27 18:37:53.984 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4973 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:37:31"]
2025-08-27 18:38:33.707 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 39719 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:37:31"]
2025-08-27 18:38:39.232 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4044 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:38:12"]
2025-08-27 18:38:41.330 [async-task-pool118] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5612 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:38:28"]
2025-08-27 18:38:43.500 [async-task-pool123] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9069 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:38:28"]
2025-08-27 18:38:50.173 [async-task-pool123] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6670 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:38:28"]
2025-08-27 18:39:27.223 [async-task-pool123] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37046 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:38:28"]
2025-08-27 18:39:31.583 [async-task-pool157] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3186 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:39:04"]
2025-08-27 18:39:33.989 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5120 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:39:06"]
2025-08-27 18:39:35.849 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7924 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:39:04"]
2025-08-27 18:39:42.328 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6476 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:39:04"]
2025-08-27 18:40:19.019 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36686 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:39:04"]
2025-08-27 18:40:25.898 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4377 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:40:04"]
2025-08-27 18:40:27.821 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5887 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:39:44"]
2025-08-27 18:40:29.497 [async-task-pool16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8957 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:40:06"]
2025-08-27 18:40:36.188 [async-task-pool16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6687 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:40:06"]
2025-08-27 18:43:59.449 [pool-5-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1226 millis. SELECT
        t1.id,
        t1.deduction_date,
        t1.deduction_type,
        t1.deduction_level,
        t1.deduction_score,
        t1.user_id,
        t1.department_id,
        t1.risk_type,
        t1.reference_id,
        t1.created_time,
        t1.created_by
        FROM
        (SELECT * FROM tbl_deduction_detail WHERE is_del = '1') t1
         
         
         
         
         WHERE  t1.deduction_type = ? 
        order by t1.deduction_date desc["主机漏洞"]
2025-08-27 18:44:08.096 [async-task-pool13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3435 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:43:34"]
2025-08-27 18:44:11.172 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5935 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:43:29"]
2025-08-27 18:44:11.886 [pool-4-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getFirewallList,458] - 图幻-----获取防火墙列表-----失败： SSLHandshakeException: Remote host terminated the handshake
2025-08-27 18:44:12.533 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8460 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:43:37"]
2025-08-27 18:44:17.252 [pool-4-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getNatList,492] - 图幻-----获取防火墙NAT列表--失败：SSLHandshakeException: Remote host terminated the handshake
2025-08-27 18:44:21.466 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8521 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:43:37"]
2025-08-27 18:44:32.092 [pool-13-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4793 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         ) ) and ta.handle_state=0["***************","***************","***************","***************"]
2025-08-27 18:44:32.097 [pool-13-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4813 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-27 18:44:32.101 [pool-13-thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4824 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-27 18:44:32.338 [pool-13-thread-4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5075 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) ) and ta.handle_state=0["**************","***************","***************","**************","***************","***************"]
2025-08-27 18:45:00.033 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38526 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:43:37"]
2025-08-27 18:45:04.821 [async-task-pool76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3656 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:44:34"]
2025-08-27 18:45:07.504 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5927 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:44:30"]
2025-08-27 18:45:10.090 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9609 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:44:35"]
2025-08-27 18:45:17.495 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7387 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:44:35"]
2025-08-27 18:45:51.519 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34015 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:44:35"]
2025-08-27 18:45:56.289 [async-task-pool131] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3866 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:45:34"]
2025-08-27 18:45:59.994 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7055 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:45:35"]
2025-08-27 18:46:03.874 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11991 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:45:42"]
2025-08-27 18:46:10.620 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6733 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:45:42"]
2025-08-27 18:46:49.919 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 39294 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:45:42"]
2025-08-27 18:46:54.280 [async-task-pool197] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3266 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:46:34"]
2025-08-27 18:46:56.858 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5377 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:46:30"]
2025-08-27 18:46:59.972 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9566 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:46:39"]
2025-08-27 18:47:05.738 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5759 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:46:39"]
2025-08-27 18:47:43.685 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37944 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:46:39"]
2025-08-27 18:47:48.209 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3098 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:47:09"]
2025-08-27 18:47:51.692 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6116 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:47:07"]
2025-08-27 18:47:56.063 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11663 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:47:09"]
2025-08-27 18:48:06.528 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10461 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:47:09"]
2025-08-27 18:48:43.292 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36760 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:47:09"]
2025-08-27 18:48:47.779 [async-task-pool122] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3470 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:48:12"]
2025-08-27 18:48:50.468 [async-task-pool121] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5733 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:48:11"]
2025-08-27 18:48:54.460 [async-task-pool123] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10672 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:48:10"]
2025-08-27 18:49:00.313 [async-task-pool123] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5848 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:48:10"]
2025-08-27 18:49:35.426 [async-task-pool123] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 35103 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:48:10"]
2025-08-27 18:49:39.453 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3143 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:49:09"]
2025-08-27 18:49:42.102 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5366 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:49:16"]
2025-08-27 18:49:47.313 [async-task-pool179] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11589 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:49:14"]
2025-08-27 18:49:54.209 [async-task-pool179] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6893 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:49:14"]
2025-08-27 18:50:27.030 [async-task-pool179] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 32818 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:49:14"]
2025-08-27 18:50:32.122 [async-task-pool46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3032 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:50:12"]
2025-08-27 18:50:35.015 [async-task-pool45] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5540 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:50:16"]
2025-08-27 18:50:39.620 [async-task-pool48] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10998 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:50:06"]
2025-08-27 18:50:44.778 [async-task-pool48] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5155 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:50:06"]
2025-08-27 18:51:18.321 [async-task-pool48] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 33538 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:50:06"]
2025-08-27 18:51:24.135 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4083 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:50:34"]
2025-08-27 18:51:27.373 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6924 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:50:46"]
2025-08-27 18:51:30.962 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11430 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:50:47"]
2025-08-27 18:51:38.602 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7636 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:50:47"]
2025-08-27 18:52:11.085 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 32479 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:50:47"]
2025-08-27 18:52:17.976 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3678 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:51:34"]
2025-08-27 18:52:21.601 [async-task-pool190] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6782 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:51:48"]
2025-08-27 18:52:25.819 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12048 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:51:50"]
2025-08-27 18:52:33.724 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7901 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:51:50"]
2025-08-27 18:53:05.773 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 32046 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:51:50"]
2025-08-27 18:53:09.894 [async-task-pool60] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2659 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:52:34"]
2025-08-27 18:53:17.040 [async-task-pool69] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5724 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:52:34"]
2025-08-27 18:53:21.335 [async-task-pool68] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9629 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:52:48"]
2025-08-27 18:53:24.916 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14374 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:52:46"]
2025-08-27 18:53:29.916 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4997 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:52:46"]
2025-08-27 18:54:02.904 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 32984 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:52:46"]
2025-08-27 18:54:10.280 [async-task-pool167] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2646 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:53:34"]
2025-08-27 18:54:13.844 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5656 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:53:51"]
2025-08-27 18:54:16.625 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9704 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:53:37"]
2025-08-27 18:54:21.855 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5226 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:53:37"]
2025-08-27 18:54:56.312 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34451 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:53:37"]
2025-08-27 18:55:00.816 [async-task-pool32] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2821 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:54:34"]
2025-08-27 18:55:06.418 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4257 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:54:34"]
2025-08-27 18:55:10.326 [async-task-pool44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7655 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:54:54"]
2025-08-27 18:55:13.671 [async-task-pool43] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12213 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:54:52"]
2025-08-27 18:55:21.122 [async-task-pool43] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7447 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:54:52"]
2025-08-27 18:55:54.326 [async-task-pool43] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 33200 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:54:52"]
2025-08-27 18:55:59.815 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3480 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:55:10"]
2025-08-27 18:56:02.616 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5819 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:55:23"]
2025-08-27 18:56:05.698 [async-task-pool118] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9982 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:55:21"]
2025-08-27 18:56:12.704 [async-task-pool118] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7003 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:55:21"]
2025-08-27 18:56:48.952 [async-task-pool118] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36244 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:55:21"]
2025-08-27 18:56:54.190 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3094 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:56:11"]
2025-08-27 18:56:57.287 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5698 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:56:27"]
2025-08-27 18:56:59.765 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9226 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:56:13"]
2025-08-27 18:57:05.365 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5595 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:56:13"]
2025-08-27 18:57:45.828 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 40460 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:56:13"]
2025-08-27 18:57:50.229 [async-task-pool58] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3491 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:57:10"]
2025-08-27 18:57:53.693 [async-task-pool44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6523 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:57:30"]
2025-08-27 18:57:56.375 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10185 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:57:27"]
2025-08-27 18:58:01.836 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5457 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:57:27"]
2025-08-27 18:58:36.533 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34693 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:57:27"]
2025-08-27 18:58:41.389 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3390 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:58:12"]
2025-08-27 18:58:44.929 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6428 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:58:30"]
2025-08-27 18:58:47.171 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9809 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:58:27"]
2025-08-27 18:58:53.265 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6091 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:58:27"]
2025-08-27 18:59:08.823 [async-task-pool155] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1810 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-27 18:57:06",4]
2025-08-27 18:59:08.823 [async-task-pool150] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1797 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-27 18:57:06",1]
2025-08-27 18:59:08.838 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2675 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-27 18:59:06","/v2/flow-bypass-filtering-log"]
2025-08-27 18:59:08.838 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2676 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-27 18:59:06","/v2/flow-bypass-filtering-log"]
2025-08-27 18:59:08.862 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6069 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",5,"sensitive_info",37,"weak_password",1,"['账号:ftpuser, 密码:******']",5,"['36**************33', '43**************35', '36**************60', '36**************1X', '36*****...",37,"['账号:admin, 密码:******', '账号:glp, 密码:******', '账号:13800001122, 密码:******', '账号:13800001121, 密码:***...",1,"********",5,"********",37,"********",1,"2024-10-31 11:52:33",5,"2024-10-30 14:22:32",37,"2025-07-02 10:49:00",1,"2025-08-27 18:35:00",5,"2025-08-27 15:26:59",37,"2025-08-27 10:45:02",1,4,5,4,37,4,1,5,37]
2025-08-27 18:59:08.868 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6074 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",5,"sensitive_info",37,"weak_password",1,"['账号:ftpuser, 密码:******']",5,"['36**************33', '43**************35', '36**************60', '36**************1X', '36*****...",37,"['账号:admin, 密码:******', '账号:glp, 密码:******', '账号:13800001122, 密码:******', '账号:13800001121, 密码:***...",1,"********",5,"********",37,"********",1,"2024-10-31 11:52:33",5,"2024-10-30 14:22:32",37,"2025-07-02 10:49:00",1,"2025-08-27 18:35:00",5,"2025-08-27 15:26:59",37,"2025-08-27 10:45:02",1,1,5,1,37,1,1,5,37]
2025-08-27 18:59:08.982 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10471 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",41372,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","yEYBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-27 18:58:52",4]
2025-08-27 18:59:09.027 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10296 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",41372,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","yEYBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-27 18:58:52",1]
2025-08-27 18:59:31.173 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37905 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:58:27"]
2025-08-27 18:59:35.955 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3055 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:59:09"]
2025-08-27 18:59:38.197 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4887 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 18:59:29"]
2025-08-27 18:59:40.621 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8283 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 18:59:30"]
2025-08-27 18:59:46.245 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5372 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 18:59:30"]
2025-08-27 19:00:31.796 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 45415 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 18:59:30"]
2025-08-27 19:00:36.432 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3248 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:00:12"]
2025-08-27 19:00:40.422 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8079 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:00:28"]
2025-08-27 19:00:45.156 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4731 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:00:28"]
2025-08-27 19:01:22.990 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37831 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:00:28"]
2025-08-27 19:01:29.530 [async-task-pool127] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4551 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:01:00"]
2025-08-27 19:01:32.660 [async-task-pool122] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7146 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:01:02"]
2025-08-27 19:01:34.763 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10418 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:01:05"]
2025-08-27 19:01:39.311 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4543 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:01:05"]
2025-08-27 19:02:15.121 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 35807 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:01:05"]
2025-08-27 19:02:22.449 [async-task-pool4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5044 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:02:04"]
2025-08-27 19:02:26.029 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8094 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:02:07"]
2025-08-27 19:02:28.490 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11827 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:02:05"]
2025-08-27 19:02:33.929 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5436 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:02:05"]
2025-08-27 19:03:10.170 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36237 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:02:05"]
2025-08-27 19:03:17.653 [async-task-pool82] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4107 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:03:09"]
2025-08-27 19:03:20.650 [async-task-pool79] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6515 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:03:10"]
2025-08-27 19:03:22.799 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10154 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:02:59"]
2025-08-27 19:03:27.375 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4573 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:02:59"]
2025-08-27 19:04:05.035 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37657 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:02:59"]
2025-08-27 19:04:09.223 [async-task-pool131] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3167 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:03:34"]
2025-08-27 19:04:11.591 [async-task-pool122] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5117 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:03:40"]
2025-08-27 19:04:13.720 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8350 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:03:31"]
2025-08-27 19:04:18.794 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5071 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:03:31"]
2025-08-27 19:05:00.110 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 41314 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:03:31"]
2025-08-27 19:05:06.596 [async-task-pool13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3049 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:04:34"]
2025-08-27 19:05:08.720 [async-task-pool17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4727 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:04:38"]
2025-08-27 19:05:10.909 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7948 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:04:42"]
2025-08-27 19:05:15.898 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4986 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:04:42"]
2025-08-27 19:05:54.499 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38597 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:04:42"]
2025-08-27 19:05:59.014 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2754 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:05:34"]
2025-08-27 19:06:00.871 [async-task-pool86] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4216 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:05:32"]
2025-08-27 19:06:04.640 [async-task-pool97] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2943 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:05:34"]
2025-08-27 19:06:06.469 [async-task-pool95] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4453 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:05:32"]
2025-08-27 19:06:12.285 [async-task-pool120] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3738 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:05:34"]
2025-08-27 19:06:14.736 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5663 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:05:32"]
2025-08-27 19:06:17.257 [async-task-pool125] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9574 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:05:35"]
2025-08-27 19:06:21.668 [async-task-pool125] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4407 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:05:35"]
2025-08-27 19:06:53.300 [async-task-pool125] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 31629 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:05:35"]
2025-08-27 19:06:56.915 [async-task-pool123] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2286 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:06:34"]
2025-08-27 19:07:03.206 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4335 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:06:34"]
2025-08-27 19:07:06.090 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6678 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:06:38"]
2025-08-27 19:07:08.482 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10321 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:06:39"]
2025-08-27 19:07:14.840 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6356 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:06:39"]
2025-08-27 19:07:49.408 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34562 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:06:39"]
2025-08-27 19:08:01.065 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5194 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:07:34"]
2025-08-27 19:08:04.288 [async-task-pool133] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7682 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:07:31"]
2025-08-27 19:08:06.519 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11413 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:07:45"]
2025-08-27 19:08:13.588 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7066 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:07:45"]
2025-08-27 19:08:51.648 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38055 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:07:45"]
2025-08-27 19:09:03.012 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3971 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:08:34"]
2025-08-27 19:09:05.991 [async-task-pool61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6510 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:08:45"]
2025-08-27 19:09:08.149 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9710 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:08:37"]
2025-08-27 19:09:13.317 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5163 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:08:37"]
2025-08-27 19:09:50.244 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36918 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:08:37"]
2025-08-27 19:09:59.308 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2693 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:09:34"]
2025-08-27 19:10:03.254 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7655 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:09:30"]
2025-08-27 19:10:08.642 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5382 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:09:30"]
2025-08-27 19:10:37.771 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29125 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:09:30"]
2025-08-27 19:10:44.979 [async-task-pool60] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3631 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:10:12"]
2025-08-27 19:10:49.264 [async-task-pool66] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8521 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:10:05"]
2025-08-27 19:10:53.985 [async-task-pool66] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4717 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:10:05"]
2025-08-27 19:11:31.511 [async-task-pool66] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37523 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:10:05"]
2025-08-27 19:11:42.646 [async-task-pool5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3349 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:11:10"]
2025-08-27 19:11:44.986 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5249 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:11:19"]
2025-08-27 19:11:47.151 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8448 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:11:14"]
2025-08-27 19:11:54.431 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7272 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:11:14"]
2025-08-27 19:12:30.336 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 35901 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:11:14"]
2025-08-27 19:12:39.150 [async-task-pool117] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2965 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:12:11"]
2025-08-27 19:12:45.139 [async-task-pool116] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9602 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:12:05"]
2025-08-27 19:12:50.644 [async-task-pool116] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5502 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:12:05"]
2025-08-27 19:13:26.771 [async-task-pool116] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36119 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:12:05"]
2025-08-27 19:13:34.695 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3118 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:13:09"]
2025-08-27 19:13:41.060 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4140 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:13:09"]
2025-08-27 19:13:43.290 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5762 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:13:18"]
2025-08-27 19:13:45.506 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9441 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:13:19"]
2025-08-27 19:13:50.351 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4842 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:13:19"]
2025-08-27 19:14:31.085 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 40731 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:13:19"]
2025-08-27 19:14:35.110 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3198 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:14:12"]
2025-08-27 19:14:40.621 [async-task-pool134] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3748 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:14:12"]
2025-08-27 19:14:42.835 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5546 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:14:17"]
2025-08-27 19:14:44.689 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8356 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:14:20"]
2025-08-27 19:14:49.578 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4513 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:14:20"]
2025-08-27 19:15:27.727 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38146 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:14:20"]
2025-08-27 19:15:32.902 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3576 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:15:10"]
2025-08-27 19:15:35.443 [async-task-pool186] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5525 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:15:17"]
2025-08-27 19:15:37.567 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8721 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:15:21"]
2025-08-27 19:15:43.275 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5705 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:15:21"]
2025-08-27 19:16:21.767 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38482 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:15:21"]
2025-08-27 19:16:25.817 [async-task-pool53] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3198 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:15:34"]
2025-08-27 19:16:28.730 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5692 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:15:36"]
2025-08-27 19:16:31.288 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9255 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:15:32"]
2025-08-27 19:16:36.098 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4807 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:15:32"]
2025-08-27 19:17:15.212 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 39111 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:15:32"]
2025-08-27 19:17:20.537 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3859 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:16:34"]
2025-08-27 19:17:22.615 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5351 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:16:38"]
2025-08-27 19:17:27.632 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3726 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:16:34"]
2025-08-27 19:17:29.852 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5649 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:16:38"]
2025-08-27 19:17:32.035 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8700 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:16:45"]
2025-08-27 19:17:36.686 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4649 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:16:45"]
2025-08-27 19:18:19.614 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 42923 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:16:45"]
2025-08-27 19:18:24.033 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3623 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:17:34"]
2025-08-27 19:18:24.540 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4650 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:17:48"]
2025-08-27 19:18:26.192 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5282 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:17:46"]
2025-08-27 19:18:29.653 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5110 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:17:48"]
2025-08-27 19:19:15.351 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 45695 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:17:48"]
2025-08-27 19:19:20.095 [async-task-pool45] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3418 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:18:34"]
2025-08-27 19:19:23.394 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5954 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:18:45"]
2025-08-27 19:19:25.320 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9087 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:18:55"]
2025-08-27 19:19:29.708 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4384 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:18:55"]
2025-08-27 19:20:07.925 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38213 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:18:55"]
2025-08-27 19:20:11.959 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2744 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:19:34"]
2025-08-27 19:20:17.053 [async-task-pool127] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7497 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:19:45"]
2025-08-27 19:20:19.527 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11007 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:19:47"]
2025-08-27 19:20:24.319 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4789 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:19:47"]
2025-08-27 19:21:00.842 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36521 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:19:47"]
2025-08-27 19:21:04.447 [async-task-pool118] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2852 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:20:12"]
2025-08-27 19:21:07.937 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1216 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-27 19:20:50"]
2025-08-27 19:21:12.024 [async-task-pool197] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5135 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:20:34"]
2025-08-27 19:21:14.698 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6757 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:20:50"]
2025-08-27 19:21:16.762 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10847 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:20:39"]
2025-08-27 19:21:21.549 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4783 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:20:39"]
2025-08-27 19:21:39.195 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10985 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-27 19:21:28","/v2/flow-alarm-detail"]
2025-08-27 19:21:39.202 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10962 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-27 19:21:28","/v2/flow-alarm-detail"]
2025-08-27 19:21:39.204 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6216 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-27 19:19:32",1]
2025-08-27 19:21:39.213 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9050 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-27 19:21:30","/v2/flow-bypass-filtering-log"]
2025-08-27 19:21:39.214 [async-task-pool13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9051 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-27 19:21:30","/v2/flow-bypass-filtering-log"]
2025-08-27 19:21:39.219 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6231 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-27 19:19:32",4]
2025-08-27 19:21:39.223 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7310 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",5,"sensitive_info",37,"weak_password",1,"['账号:ftpuser, 密码:******']",5,"['36**************33', '43**************35', '36**************60', '36**************1X', '36*****...",37,"['账号:admin, 密码:******', '账号:glp, 密码:******', '账号:13800001122, 密码:******', '账号:13800001121, 密码:***...",1,"********",5,"********",37,"********",1,"2024-10-31 11:52:33",5,"2024-10-30 14:22:32",37,"2025-07-02 10:49:00",1,"2025-08-27 19:05:00",5,"2025-08-27 15:26:59",37,"2025-08-27 10:45:02",1,1,5,1,37,1,1,5,37]
2025-08-27 19:21:39.224 [async-task-pool20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7320 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",5,"sensitive_info",37,"weak_password",1,"['账号:ftpuser, 密码:******']",5,"['36**************33', '43**************35', '36**************60', '36**************1X', '36*****...",37,"['账号:admin, 密码:******', '账号:glp, 密码:******', '账号:13800001122, 密码:******', '账号:13800001121, 密码:***...",1,"********",5,"********",37,"********",1,"2024-10-31 11:52:33",5,"2024-10-30 14:22:32",37,"2025-07-02 10:49:00",1,"2025-08-27 19:05:00",5,"2025-08-27 15:26:59",37,"2025-08-27 10:45:02",1,4,5,4,37,4,1,5,37]
2025-08-27 19:21:51.150 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29597 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:20:39"]
2025-08-27 19:21:56.679 [async-task-pool46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4224 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:21:09"]
2025-08-27 19:22:02.882 [async-task-pool64] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4430 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:21:09"]
2025-08-27 19:22:06.716 [async-task-pool45] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7877 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:21:23"]
2025-08-27 19:22:08.818 [async-task-pool47] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10936 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:21:31"]
2025-08-27 19:22:14.582 [async-task-pool47] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5760 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:21:31"]
2025-08-27 19:22:50.690 [async-task-pool47] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36105 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:21:31"]
2025-08-27 19:22:58.128 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4984 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:22:34"]
2025-08-27 19:23:01.051 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7425 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:22:32"]
2025-08-27 19:23:03.122 [async-task-pool151] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10702 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:22:23"]
2025-08-27 19:23:07.788 [async-task-pool151] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4663 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:22:23"]
2025-08-27 19:23:42.664 [async-task-pool151] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34872 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:22:23"]
2025-08-27 19:23:47.650 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4105 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:23:10"]
2025-08-27 19:23:50.673 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6660 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:23:24"]
2025-08-27 19:23:52.568 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9529 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:23:16"]
2025-08-27 19:23:57.303 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4732 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:23:16"]
2025-08-27 19:24:32.962 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 35656 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:23:16"]
2025-08-27 19:24:39.432 [async-task-pool81] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4568 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:24:04"]
2025-08-27 19:24:43.008 [async-task-pool61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7661 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:24:06"]
2025-08-27 19:24:45.215 [async-task-pool83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10825 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:23:59"]
2025-08-27 19:24:49.980 [async-task-pool83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4762 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:23:59"]
2025-08-27 19:25:19.949 [async-task-pool83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29964 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:23:59"]
2025-08-27 19:25:24.036 [async-task-pool120] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3288 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:25:10"]
2025-08-27 19:25:26.063 [async-task-pool128] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5004 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:25:07"]
2025-08-27 19:25:27.524 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7348 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:25:00"]
2025-08-27 19:25:31.888 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4360 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:25:00"]
2025-08-27 19:26:03.212 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 31321 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-27 19:25:00"]
2025-08-27 19:26:11.118 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6555 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:25:34"]
2025-08-27 19:26:12.548 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7450 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-27 19:25:32"]
2025-08-27 19:26:14.853 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11342 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-27 19:25:35"]
2025-08-27 19:26:19.249 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4375 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-27 19:25:35"]
