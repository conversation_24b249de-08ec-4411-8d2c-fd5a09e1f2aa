<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-position="right"
          label-width="70px"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label-width="100px" label="最近告警时间">
                <el-date-picker
                  v-model="rangeTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="攻击者IP" prop="destIp">
                <el-input
                  v-model="queryParams.attackIp"
                  placeholder="请输入完整的攻击者IP"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="地理位置" prop="location">
                <el-select v-model="queryParams.location" clearable placeholder="请选择">
                  <el-option
                    v-for="item in locationOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleQuery"
                >查询</el-button>
                <el-button class="btn2" size="small" @click="resetQuery"
                >重置</el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-down" @click="showAll=true" v-if="!showAll">
                  展开
                </el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-up" @click="showAll=false" v-else>收起
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-if="showAll">
            <el-col :span="6">
              <el-form-item label="阻断状态" prop="blockStatus">
                <el-select v-model="queryParams.blockStatus" clearable placeholder="请选择">
                  <el-option
                    v-for="item in blockStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
<!--            <el-col :span="6">
              <el-form-item label="告警等级" prop="riskLevel">
                <el-select v-model="queryParams.riskLevel" clearable placeholder="请选择">
                  <el-option
                    v-for="item in riskLevelOptions"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>-->
          </el-row>
        </el-form>
      </div>
      <div class="custom-content-container" :style="showAll ? { height: 'calc(100% - 248px)' } :{ height: 'calc(100% - 208px)' }">
        <div class="common-header">
          <div><span class="common-head-title">攻击者视角列表</span></div>
          <div style="width: 50%; margin-left: 8%">
<!--            <attack-stage-text ref="stage"/>-->
          </div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  :disabled="multiple"
                  @click="showHandleBatch"
                >批量处置
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleBlocking"
                >批量阻断</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleExport"
                >导出
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="tableContainer">
        <el-table
          height="100%"
          v-loading="loading"
          :data="attackAlarmList"
          @selection-change="handleSelectionChange"
          @expand-change="expandChange" >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="攻击者IP" min-width="150" prop="attackIp" :show-overflow-tooltip="false">
            <template slot-scope="scope">
              <div style="display: flex; align-items: center; justify-content: flex-start">
                <span>{{ scope.row.attackIp }}</span>
                <img v-if="scope.row.isBlocking" style="width: 24px;margin-left: 10px" src="@/assets/images/block.png" alt="">
              </div>
            </template>
          </el-table-column>
          <el-table-column label="关联业务系统" prop="businessApplicationList" width="130">
            <template slot-scope="scope">
              <el-tooltip placement="bottom-end" effect="light" v-if="scope.row.businessApplications && scope.row.businessApplications.length > 0">
                <div slot="content">
                  <div v-for="(item,tagIndex) in scope.row.businessApplications" :key="item.assetId" class="overflow-tag" v-if="tagIndex <= 5">
                    <el-tag type="primary"><span>{{item.assetName}}</span></el-tag>
                  </div>
                  <div v-if="scope.row.businessApplications.length > 5">
                    <el-tag type="primary"><span>...</span></el-tag>
                  </div>
                </div>
                <el-tag type="primary" class="asset-tag"><span>{{handleApplicationTagShow(scope.row.businessApplications)}}</span></el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="告警等级" prop="riskLevel" width="150">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.alarm_attack_risk_level" :value="scope.row.riskLevel"/>
            </template>
          </el-table-column>
          <el-table-column
            label="攻击者标签"
            prop="tags"
            :width="flexColumnWidth"
            :show-overflow-tooltip="false">
            <template slot-scope="scope">
              <div style="display: flex; align-items: center; justify-content: flex-start">
                <span class="tag-name" v-for="(tag,index) in scope.row.tags" :style="{ backgroundColor: tagBackgroundColor[index], color: tagColor[index], marginRight: scope.row.tags.length > 0 ? '5px' : '0'}">{{ tag.tagName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="地理位置" min-width="120" prop="location"  />
          <el-table-column label="攻击目标IP数" min-width="120" prop="victimIpNums">
            <template slot-scope="scope">
              <el-button type="text" @click="openDipDetails(scope.row)">{{scope.row.victimIpNums}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="命中规则数" min-width="120" prop="attackTypeNums">
            <template slot-scope="scope">
              <el-button type="text" @click="openThreatenNameDetails(scope.row)">{{scope.row.attackTypeNums}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="告警数量" min-width="120" prop="attackNums"  />
          <el-table-column label="最早告警时间" min-width="120" prop="startTime"  />
          <el-table-column label="最近告警时间" min-width="120" prop="updateTime"  />
          <el-table-column label="同步状态" prop="synchronizationStatus" width="150">
            <template slot-scope="scope">
              <span v-if="scope.row.synchronizationStatus === '0'">未同步</span>
              <span v-else-if="scope.row.synchronizationStatus === '1'">已同步</span>
            </template>
          </el-table-column>
          <el-table-column label="处置状态" prop="handleState" width="120" :formatter="handleStateFormatter"/>
          <el-table-column label="操作" width="150" fixed="right" :show-overflow-tooltip="false" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button v-if="!(scope.row.handleState == 1 || scope.row.handleState == 3)"
                         size="mini"
                         type="text"
                         @click="showHandle(scope.row)"
                         v-hasPermi="['frailty:loophole:edit']"
              >处置
              </el-button>
              <el-button
                :disabled="!scope.row.attackIp"
                size="mini"
                type="text"
                @click="handleDetail(scope.row)"
              >详情
              </el-button>
              <el-button
                :disabled="!scope.row.attackIp"
                size="mini"
                type="text"
                @click="handleBlocking(scope.row)"
              >阻断
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        </div>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <el-dialog
      title="快速处置"
      :visible.sync="showHandleDialog"
      width="600px"
      append-to-body
    >
      <el-form ref="form" :model="handleForm" :rules="handleRules" label-width="106px">
        <el-form-item label="处置状态" prop="category">
          <el-select
            v-model="handleForm.handleState"
            placeholder="请选择处置状态"
          >
            <el-option
              v-for="dict in handleStateOption"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处置说明" prop="handleDesc">
          <el-input type="textarea" :rows="2" v-model="handleForm.handleDesc" maxlength="120" show-word-limit
                    placeholder="请输入处置说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitHandleForm">确 定</el-button>
        <el-button @click="showHandleDialog = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="批量处置"
      :visible.sync="showHandleBatchDialog"
      width="600px"
      append-to-body
    >
      <el-form ref="form" :model="handleForm" :rules="handleRules" label-width="106px">
        <el-form-item label="处置状态" prop="category">
          <el-select
            v-model="handleForm.handleState"
            placeholder="请选择处置状态"
          >
            <el-option
              v-for="dict in handleStateOption"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处置说明" prop="handleDesc">
          <el-input type="textarea" :rows="2" v-model="handleForm.handleDesc" maxlength="120" show-word-limit
                    placeholder="请输入处置说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitHandleBatchForm">确 定</el-button>
        <el-button @click="showHandleBatchDialog = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="详情" :visible.sync="detailDialog" width="70%" append-to-body>
      <detail-info v-if="detailDialog" :host-ip="hostIp" :detail-type="detailType" :is-asset="isAsset" :current-asset-data="currentAssetData" />
    </el-dialog>

<!--    <el-drawer
      :visible.sync="dipDrawerVisible"
      direction="rtl"
      size="50%">
      <el-table :data="dipDetailsData" v-loading="dipDrawerLoading">
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column property="sip" label="攻击IP"></el-table-column>
        <el-table-column property="dip" label="目标IP" :render-header="dipRenderHeader">
          <template slot-scope="scope">
            <span>{{scope.row.dip}}</span>
            <span style="color: #ADADAD;margin-left: 5px;" v-if="scope.row.serverName">[{{scope.row.serverName}}]</span>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>-->

    <el-dialog
      :visible.sync="dipDrawerVisible"
      @close="victimIpNumTotal = 0"
      width="60%" height="50%" class="dip-dialog">
      <el-table :data="dipDetailsData" v-loading="dipDrawerLoading">
<!--         <el-table-column type="index" label="序号" width="80"></el-table-column>-->
         <el-table-column property="sip" label="攻击IP"></el-table-column>
         <el-table-column property="dip" label="目标IP" :render-header="dipRenderHeader">
           <template slot-scope="scope">
             <span>{{scope.row.dip}}</span>
             <span style="color: #ADADAD;margin-left: 5px;" v-if="scope.row.serverName">[{{scope.row.serverName}}]</span>
           </template>
         </el-table-column>
      </el-table>
      <pagination
        v-show="victimIpNumTotal>0"
        :total="victimIpNumTotal"
        :page.sync="victimIpNumQueryParams.pageNum"
        :limit.sync="victimIpNumQueryParams.pageSize"
        @pagination="openDipDetails(victimIpNumData)"
      />
    </el-dialog>

    <el-dialog
      :visible.sync="threatenNameDrawerVisible"
      @close="hitRulesTotal = 0"
      width="60%" height="50%" class="dip-dialog">
      <el-table :data="threatenNameDetailsData" v-loading="threatenNameDrawerLoading">
<!--        <el-table-column type="index" label="序号" width="80"></el-table-column>-->
        <el-table-column property="sip" label="攻击IP"></el-table-column>
        <el-table-column property="threatenName" label="[规则ID] 告警名称">
          <template slot-scope="scope">
            <span :class="scope.row.alarmLevel?scope.row.alarmLevel===4?'threatenName-error':scope.row.alarmLevel===3?'threatenName-warn':scope.row.alarmLevel===2?'threatenName-success':'':''">{{scope.row.threatenName}}</span>
          </template>
        </el-table-column>
        <el-table-column property="threatenType" label="攻击类型"></el-table-column>
        <el-table-column property="count" label="告警数量"></el-table-column>
      </el-table>
      <pagination
        v-show="hitRulesTotal>0"
        :total="hitRulesTotal"
        :page.sync="hitRulesQueryParams.pageNum"
        :limit.sync="hitRulesQueryParams.pageSize"
        @pagination="openThreatenNameDetails(threatenNameDetailsQuery)"
      />
    </el-dialog>

<!--    <el-drawer
      :visible.sync="threatenNameDrawerVisible"
      direction="rtl"
      size="50%">
      <el-table :data="threatenNameDetailsData" v-loading="threatenNameDrawerLoading">
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column property="sip" label="攻击IP"></el-table-column>
        <el-table-column property="threatenName" label="[规则ID] 告警名称">
          <template slot-scope="scope">
            <span :class="scope.row.alarmLevel?scope.row.alarmLevel===4?'threatenName-error':scope.row.alarmLevel===3?'threatenName-warn':scope.row.alarmLevel===2?'threatenName-success':'':''">{{scope.row.threatenName}}</span>
          </template>
        </el-table-column>
        <el-table-column property="threatenType" label="攻击类型"></el-table-column>
        <el-table-column property="count" label="告警数量"></el-table-column>
      </el-table>
    </el-drawer>-->

    <batch-block :visible.sync="blockingDialogVisible" ref="batchBlock" />
  </div>
</template>

<script>
import { listAttackAlarm,getDipDetails,getThreatenNameDetails,handleVuln,handleBatchVuln } from "@/api/threaten/AttackAlarm";
import { parseTime } from "../../../../utils/ruoyi";
import AttackStage from "../../../threat/overview/attackStage";
import DetailInfo from "./attack_detail/index.vue"
import DeptSelect from '@/views/components/select/deptSelect.vue'
import AttackStageText from '@/views/threat/overview/attackStageText.vue'
import {addBlockIp} from "@/api/threaten/threatenWarn";
import BatchBlock from "@/views/frailty/event/component/batchBlock.vue";
import {getMulTypeDict} from "@/api/system/dict/data";

export default {
  name: "attackViewList",
  components: { AttackStageText, DeptSelect, AttackStage, DetailInfo,BatchBlock },
  dicts: ['alarm_attack_risk_level'],
  props: {
    propsActiveName: {
      type: String
    },
    propsQueryParams: {
      type: Object,
      default: function () {
        return null
      }
    },
    currentBtn: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      showHandleBatchDialog: false,
      multiple: true,
      handleRules: {},
      showHandleDialog: false,
      victimIpNumData: {},
      threatenNameDetailsQuery:{},
      count: 10,
      showAll: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      victimIpNumQueryParams: {
        pageNum: 1,
        pageSize: 10
      },
      hitRulesQueryParams: {
        pageNum: 1,
        pageSize: 10
      },
      rangeTime: null,
      loading: false,
      descLoading: false,
      attackAlarmList: [],
      total: 0,
      victimIpNumTotal: 0,
      hitRulesTotal: 0,
      detailDialog: false,
      descKey: 0,
      hostIp: '',
      detailType: 'attack',
      isAsset: false,
      currentAssetData: {},
      dipDrawerVisible: false,
      dipDetailsData: [],
      ids: [],
      dipDrawerLoading: false,
      threatenName: 1,
      threatenNameDrawerVisible: false,
      threatenNameDetailsData: [],
      threatenNameDrawerLoading: false,
      blockingDialogVisible: false,
      multipleSelection: [],
      locationOptions: [
        {value: '1',label: '内网'},
        {value: '2',label: '外网'},
        {value: '3',label: '内/外网'},
      ],
      blockStatusOptions: [
        {value: 1,label: '正在阻断'},
        {value: 2,label: '曾经阻断'},
      ],
      riskLevelOptions: [],
      rows: [],
      handleStateOptions: [
        {
          label: '未处置',
          value: '0'
        },
        {
          label: '已处置',
          value: '1'
        },
        {
          label: '忽略',
          value: '2'
        },
        {
          label: '处置中',
          value: '3'
        }
      ],
      handleStateOption: [
        {
          label: '已处置',
          value: '1'
        },
        {
          label: '忽略',
          value: '2'
        }
      ],
      handleForm: {
        id: '',
        handleDesc: ''
      },
      tagColor: ['#c86c00','#bf1a1a','#1a2bbf','#901abf','#1a2bbf'],
      tagBackgroundColor: ['#ff9f1933','#fd828233','#7899e033','#c278e033','#7899e033'],
    }
  },
  watch: {
    propsActiveName() {
      this.init()
    },
    propsQueryParams(val){
      this.handlePropsQuery(val);
    },
  },
  computed: {
    noMore () {
      return this.count >= 20
    },
    disabled () {
      return this.loading || this.noMore
    },
    flexColumnWidth() {
      return this.columnWidth({prop: 'tags', label: '攻击者标签'}, this.attackAlarmList);
    }
  },
  mounted() {
    let query = this.$route.query;
    if(!query || Object.keys(query).length < 1){
      this.init();
    }else {
      this.handlePropsQuery(query);
    }
    this.getRiskLevelOptions();
  },
  methods: {
    load () {
      this.loading = true
      setTimeout(() => {
        this.count += 2
        this.loading = false
      }, 2000)
    },
    handlePropsQuery(val) {
      if(val && Object.keys(val).length > 0){
        this.queryParams = val;
        if(val.startTime && val.endTime){
          this.rangeTime = [val.startTime, val.endTime];
        }
        if(val.alarmLevel){
          if(val.alarmLevel === '2'){
            this.queryParams.riskLevel = '1';
          }else if(val.alarmLevel === '3'){
            this.queryParams.riskLevel = '2';
          }else if(val.alarmLevel === '4'){
            this.queryParams.riskLevel = '3';
          }else {
            this.queryParams.riskLevel = '-99';
          }
        }else {
          this.queryParams.riskLevel = null;
        }
        this.handleQuery();
      }
    },
    showHandle(row) {
      this.handleForm = {};
      this.handleForm = {...row};
      this.$nextTick(() => {
        if (this.handleForm.handleState == 0) {
          this.handleForm.handleState = null
        }
        this.handleForm.assetName = ''
        this.showHandleDialog = true;
      })
    },
    init() {
      this.handleQuery()
      //this.getList()
    },
    handleQuery() {
      this.queryParams = {...this.queryParams,...this.propsQueryParams};
      if(this.queryParams.alarmLevel){
        if(this.queryParams.alarmLevel === '2'){
          this.queryParams.riskLevel = '1';
        }else if(this.queryParams.alarmLevel === '3'){
          this.queryParams.riskLevel = '2';
        }else if(this.queryParams.alarmLevel === '4'){
          this.queryParams.riskLevel = '3';
        }else {
          this.queryParams.riskLevel = '-99';
        }
      }else {
        this.queryParams.riskLevel = null;
      }
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      if (this.rangeTime !== null) {
        this.queryParams.startUpdateTime = parseTime(this.rangeTime[0]);
        this.queryParams.endUpdateTime = parseTime(this.rangeTime[1]);
      } else {
        this.queryParams.startUpdateTime = null;
        this.queryParams.endUpdateTime = null;
      }

      if(!this.queryParams.startUpdateTime){
        this.queryParams.startUpdateTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'); // 一周前，时间部分为 00:00:00
      }
      if(!this.queryParams.endUpdateTime){
        this.queryParams.endUpdateTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'); // 当前日期，时间部分为 23:59:59
      }
      this.rangeTime = [this.queryParams.startUpdateTime, this.queryParams.endUpdateTime];
      this.getList()
      this.$nextTick(() => {
        this.$refs.stage && this.$refs.stage.initAttackStage({
          ...this.queryParams,
          startTime: this.queryParams.startUpdateTime,
          endTime: this.queryParams.endUpdateTime
        })
      })
    },
    resetQuery() {
      this.queryParams = {
        srcIp: '',
        alarmLevel: ''
      }
      this.propsQueryParams.riskLevel = this.queryParams.riskLevel;
      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel;
      if(this.$refs.stage){
        this.$refs.stage.currentSelectedCard = null;
      }
      this.rangeTime = []
      this.handleQuery();
    },
    getList() {
      this.loading = true;
      //同步请求类型统计数据
      let params = {...this.queryParams};
      params.srcIp = this.queryParams.attackIp;
      params.startTime = this.queryParams.startUpdateTime;
      params.endTime = this.queryParams.endUpdateTime;
      if(params.riskLevel){
        if(params.riskLevel === '1'){
          params.alarmLevel = '2';
        }else if(params.riskLevel === '2'){
          params.alarmLevel = '3';
        }else if(params.riskLevel === '3'){
          params.alarmLevel = '4';
        }
      }
      this.$emit('getList',params);
      listAttackAlarm(this.queryParams).then(res => {
        if (res.code === 200) {
          this.attackAlarmList = res.rows
          this.attackAlarmList.forEach(e => {
            e.childrenData = {}
          })
          this.total = res.total
        }
      }).finally(()=>{
        this.loading = false;
      })
    },
    async expandChange(row, expandRowKeys) {
      if (expandRowKeys.length > 0) {
        getEventSegTypeList({
          attackIp: row.attackIp,
          startTime: this.queryParams.startUpdateTime,
          endTime: this.queryParams.endUpdateTime
        }).then(res => {
          if (res.code === 200) {
            const childrenData = res.data
            this.$set(row, 'childrenData', childrenData)
            this.descKey = new Date().getTime()
          }
        })
      }
    },
    handleExport() {
      this.download('/threaten/AttackAlarm/export', {
        ...this.queryParams
      }, `攻击者视角数据_${new Date().getTime()}.xlsx`)
    },
    handleDetail(row) {
      if (row.attackIp) {
        this.detailDialog = true
        this.hostIp = row.attackIp;
        this.isAsset = row.assetId;
        this.currentAssetData = row;
      }
    },
    handleAtcClick(attackSeg){
      this.queryParams.attackSeg = attackSeg;
      this.handleQuery();
    },
    handleApplicationTagShow(applicationList){
      if(!applicationList || applicationList.length < 1){
        return '';
      }
      let result = applicationList[0].assetName;
      if(applicationList.length > 1){
        result += '...';
      }
      return result;
    },
    openDipDetails(row){
      // this.victimIpNumTotal = 0 ;
        this.dipDetailsData = [];
      this.dipDrawerVisible = true;
      this.dipDrawerLoading = true;
      this.victimIpNumData = row;
      getDipDetails({
        attackIp: row.attackIp,
        startTime: row.startTime,
        updateTime: row.updateTime,
        id: row.id,
        pageSize: this.victimIpNumQueryParams.pageSize,
        pageNum: this.victimIpNumQueryParams.pageNum
      }).then(res => {
        this.dipDetailsData = res.data.rows;
        this.victimIpNumTotal = res.data.total;
      }).finally(() => {
        this.dipDrawerLoading = false;
      })
    },
    dipRenderHeader(h, { column, $index }){
      return h('div', [
        h('span', column.label),
        h('span', {
          style: {
            marginLeft: '5px',
            color: '#ADADAD'
          }
        }, '[主机名称]')
      ])
    },
    openThreatenNameDetails(row){
      this.threatenNameDetailsData = [];
      this.threatenNameDrawerVisible = true;
      this.threatenNameDrawerLoading = true;
      this.threatenNameDetailsQuery = row
      getThreatenNameDetails({
        attackIp: row.attackIp,
        startTime: row.startTime,
        updateTime: row.updateTime,
        id: row.id,
        pageSize: this.hitRulesQueryParams.pageSize,
        pageNum: this.hitRulesQueryParams.pageNum
      }).then(res => {
        this.threatenNameDetailsData = res.data.rows;
        this.hitRulesTotal = res.data.total;
      }).finally(() => {
        this.threatenNameDrawerLoading = false;
      })
    },
    handleBlocking(row) {
      console.log( row)
      let arr = [];
      if(row && row.attackIp){
        arr.push(row.attackIp);
      }else {
        if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip');
        arr = this.multipleSelection.map(item => item.attackIp);
        arr = Array.from(new Set(arr));
      }
      this.blockingDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.batchBlock && this.$refs.batchBlock.init({
          block_ip: arr.join(';')
        })
      })
    },
    // 多选
    handleSelectionChange(val) {
      this.ids = val.map(item => item.id);
      this.multiple = !val.length;
      this.multipleSelection = val;
      this.rows = val;
    },
    getRiskLevelOptions(){
      getMulTypeDict({
        dictType: 'alarm_attack_risk_level'
      }).then(res => {
        this.riskLevelOptions = res.data;
      })
    },
    columnWidth(col, list) {
      // 获取数组长度作为基础
      let arrayLength = 0;

      // 遍历数据列表，找出 tags 数组的最大长度
      for (let info of list) {
        if (info[col.prop] && Array.isArray(info[col.prop])) {
          arrayLength = Math.max(arrayLength, info[col.prop].length);
        }
      }

      // 设置最小宽度为140px
      return Math.max(arrayLength * 140, 140);
    },
    handleStateFormatter(row, column, cellValue, index) {
      let name = '未处置';
      let match = this.handleStateOptions.find(item => item.value == cellValue);
      if (match) {
        name = match.label;
      }
      return name;
    },
    submitHandleForm() {
      handleVuln(this.handleForm).then(res => {
        this.$message.success("处置成功");
        this.handleForm = {};
        this.showHandleDialog = false;
        this.getList();
        this.initData();
      })
    },
    showHandleBatch() {
      let rows = [...this.rows];
      rows = rows.filter(item => item.handleState == 0 || item.handleState == 2 || item.handleState === null);
      if (rows.length < this.rows.length) {
        this.$message.error('选择中有已处置或处置中事件，无法批量处置');
        return false;
      }
      this.handleForm = {};
      if (rows.length === 1) {
        if (rows[0].handleState == 2) {
          this.handleForm = rows[0]
        }
      }
      this.handleForm.ids = this.ids;
      this.showHandleBatchDialog = true;
    },

    submitHandleBatchForm() {
      handleBatchVuln(this.handleForm).then(res => {
        this.$message.success("处置成功");
        this.handleForm = {};
        this.showHandleBatchDialog = false;
        this.getList();
        this.initData();
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.asset-tag{
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.dip-dialog{
  ::v-deep .el-dialog{

  }
  ::v-deep .el-dialog__body{
    padding: 20px;
    margin-top: 20px;
  }
}
.attack-tag:not(:nth-child(-n+2)) {
  margin-top: 5px;
}
.overflow-tag:not(:first-child){
  margin-top: 5px;
}
.threatenName-error{
  color: #F56C6C;
}
.threatenName-success{
  color: #67C23A;
}
.threatenName-warn{
  color: #E6A23C;
}

.tag-name {
  display: inline-block;
  padding: 2px 12px;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}

::v-deep .el-dialog__body {
  max-height: 80vh;
  padding: 0 30px 30px;
  overflow-y: auto;
}
</style>
