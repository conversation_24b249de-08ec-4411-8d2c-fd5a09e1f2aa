{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\userSelect.vue?vue&type=style&index=0&id=0efe412e&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\userSelect.vue", "mtime": 1756294198405}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnRhZ3Mtc2VsZWN0LWlucHV0IDo6di1kZWVwIC5lbC1zZWxlY3RfX3RhZ3MgewogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBmbGV4LWZsb3c6IG5vd3JhcDsKICBkaXNwbGF5OmZsZXg7CiAgZmxleC13cmFwOm5vd3JhcDsKfQoudGFncy1zZWxlY3QtaW5wdXQgOjp2LWRlZXAgLmVsLXNlbGVjdF9fdGFncy10ZXh0IHsKICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgbWF4LXdpZHRoOiA5MHB4Oy8v6K6+572u5pyA5aSn5a695bqmIOi2heWHuuaYvuekui4uLgogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBmbGV4LWZsb3c6IG5vd3JhcDsKICB2ZXJ0aWNhbC1hbGlnbjpib3R0b207CiAgdGV4dC1vdmVyZmxvdzplbGxpcHNpczsKfQo="}, {"version": 3, "sources": ["userSelect.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userSelect.vue", "sourceRoot": "src/views/hhlCode/component", "sourcesContent": ["<template>\n  <div>\n    <el-select\n      ref=\"elSelect\"\n      v-model=\"selected\"\n      :placeholder=\"$attrs['placeholder']||'请选择'+this.functionName\"\n      :multiple=\"multiple\"\n      :clearable=\"clearable\"\n      class=\"tags-select-input\"\n      collapse-tags\n      @focus=\"openDialog\"\n      @clear=\"handleClear\"\n      @remove-tag=\"handleRemoveTag\"\n      v-bind=\"$attrs\">\n      <el-option\n        v-for=\"item in options\"\n        :key=\"item[valueName]\"\n        :label=\"item[labelName]+'-'+item.phonenumber\"\n        :value=\"item[valueName]\">\n      </el-option>\n    </el-select>\n    <el-dialog\n      :title=\"'选择'+this.functionName\"\n      :visible.sync=\"visible\"\n      :before-close=\"handleBeforeClose\"\n      @open=\"handleOpen\"\n      @close=\"handleClose\"\n      width=\"800px\"\n      top=\"5vh\"\n      append-to-body>\n      <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n        <!--<el-form-item label=\"账号\" prop=\"userName\">-->\n        <!--  <el-input-->\n        <!--    v-model=\"queryParams.userName\"-->\n        <!--    placeholder=\"请输入账号\"-->\n        <!--    clearable-->\n        <!--    @keyup.enter.native=\"handleQuery\"-->\n        <!--  />-->\n        <!--</el-form-item>-->\n        <el-form-item label=\"姓名\" prop=\"nickName\">\n          <el-input\n            v-model=\"queryParams.nickName\"\n            placeholder=\"请输入姓名\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item label=\"联系方式\" prop=\"phonenumber\">\n          <el-input\n            v-model=\"queryParams.phonenumber\"\n            placeholder=\"请输入联系方式\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!--<el-row :gutter=\"10\" class=\"mb8\">-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"primary\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-plus\"-->\n      <!--      size=\"mini\"-->\n      <!--      @click=\"handleAdd\"-->\n      <!--      v-hasPermi=\"['system:user:add']\"-->\n      <!--    >新增</el-button>-->\n      <!--  </el-col>-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"success\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-edit\"-->\n      <!--      size=\"mini\"-->\n      <!--      :disabled=\"single\"-->\n      <!--      @click=\"handleUpdate\"-->\n      <!--      v-hasPermi=\"['system:user:edit']\"-->\n      <!--    >修改</el-button>-->\n      <!--  </el-col>-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"danger\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-delete\"-->\n      <!--      size=\"mini\"-->\n      <!--      :disabled=\"many\"-->\n      <!--      @click=\"handleDelete\"-->\n      <!--      v-hasPermi=\"['system:user:remove']\"-->\n      <!--    >删除</el-button>-->\n      <!--  </el-col>-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"warning\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-download\"-->\n      <!--      size=\"mini\"-->\n      <!--      @click=\"handleExport\"-->\n      <!--      v-hasPermi=\"['system:user:export']\"-->\n      <!--    >导出</el-button>-->\n      <!--  </el-col>-->\n      <!--  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>-->\n      <!--</el-row>-->\n\n      <el-table ref=\"table\" v-loading=\"loading\" :data=\"list\" @select=\"handleSelect\" @select-all=\"handleSelectAll\"\n                @selection-change=\"handleSelectionChange\" @row-click=\"handleClick\" height=\"260px\">\n        <el-table-column type=\"selection\" width=\"55\" ></el-table-column>\n        <el-table-column label=\"账号\" prop=\"userName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"姓名\" prop=\"nickName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.email ? scope.row.email : '--' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"联系方式\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\"/>\n        <!--<el-table-column label=\"状态\"  prop=\"status\">-->\n        <!--  <template slot-scope=\"scope\">-->\n        <!--    <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>-->\n        <!--  </template>-->\n        <!--</el-table-column>-->\n        <!--<el-table-column label=\"创建时间\"  prop=\"createTime\" width=\"180\">-->\n        <!--  <template slot-scope=\"scope\">-->\n        <!--    <span>{{ parseTime(scope.row.createTime) }}</span>-->\n        <!--  </template>-->\n        <!--</el-table-column>-->\n        <!--        <el-table-column label=\"操作\"  class-name=\"small-padding fixed-width\">-->\n        <!--          <template slot-scope=\"scope\">-->\n        <!--            <el-button-->\n        <!--              size=\"mini\"-->\n        <!--              type=\"text\"-->\n        <!--              icon=\"el-icon-edit\"-->\n        <!--              @click=\"handleUpdate(scope.row)\"-->\n        <!--              v-hasPermi=\"['system:user:edit']\"-->\n        <!--            >修改-->\n        <!--            </el-button>-->\n        <!--            <el-button-->\n        <!--              size=\"mini\"-->\n        <!--              type=\"text\"-->\n        <!--              icon=\"el-icon-delete\"-->\n        <!--              @click=\"handleDelete(scope.row)\"-->\n        <!--              v-hasPermi=\"['system:user:remove']\"-->\n        <!--            >删除-->\n        <!--            </el-button>-->\n        <!--          </template>\n                </el-table-column>-->\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n\n      <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"700px\" append-to-body>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n          <el-row type=\"flex\" style=\"flex-wrap: wrap;\">\n            <el-col :span=\"12\">\n              <el-form-item label=\"姓名\" prop=\"nickName\">\n                <el-input v-model=\"form.nickName\" placeholder=\"请输入姓名\" maxlength=\"30\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"归属部门\" prop=\"deptId\">\n                <dept-select v-model=\"form.deptId\" is-current/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"联系方式\" prop=\"phonenumber\">\n                <el-input v-model=\"form.phonenumber\" placeholder=\"请输入联系方式\" maxlength=\"11\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"邮箱\" prop=\"email\">\n                <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"备注\">\n                <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n          <el-button @click=\"cancel\">取 消</el-button>\n        </div>\n      </el-dialog>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n        >添加{{ functionName }}\n        </el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">确定</el-button>\n        <el-button @click=\"handleCancel\">取消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {addUser, delUser, getUser, getUsers, listUser, updateUser} from \"@/api/system/user\";\nimport DeptSelect from '@/views/components/select/deptSelect';\n\nexport default {\n  name: \"UserSelect\",\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\n  components: {DeptSelect},\n  props: {\n    userdata: {type: String, default: null,},\n    // 值\n    value: {required: true},\n    // 分隔符\n    separator: {type: String, required: false, default: ','},\n    // 是否多选\n    multiple: {type: Boolean, required: false, default: false},\n    // 是否可以清空选项\n    clearable: {type: Boolean, required: false, default: true},\n  },\n  data() {\n    return {\n      // 主键字段名\n      idName: 'userId',\n      // 选项标签字段名\n      labelName: 'nickName',\n      // 选项值字段名\n      valueName: 'userId',\n      // 模块名\n      moduleName: 'system',\n      // 业务名\n      businessName: 'user',\n      // 功能名\n      functionName: '人员',\n      // 选中项\n      selected: null,\n      // 所有选中项的id和name\n      options: [],\n      // 是否显示弹出层\n      visible: false,\n      // 遮罩层\n      loading: true,\n      // 所有选中项的id\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      many: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userName: null,\n        nickName: null,\n        phonenumber: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        userName: [\n          {required: true, message: \"账号不能为空\", trigger: \"blur\"},\n          {min: 2, max: 20, message: '账号长度必须介于 2 和 20 之间', trigger: 'blur'},\n        ],\n        deptId: [\n          {required: true, message: \"部门不能为空\", trigger: \"blur\"},\n        ],\n        nickName: [\n          {required: true, message: \"姓名不能为空\", trigger: \"blur\"},\n        ],\n        password: [\n          {required: true, message: \"密码不能为空\", trigger: \"blur\"},\n          {min: 5, max: 20, message: '密码长度必须介于 5 和 20 之间', trigger: 'blur'},\n        ],\n        email: [\n          {type: \"email\", message: \"请输入正确的邮箱地址\", trigger: [\"blur\", \"change\"]},\n        ],\n        phonenumber: [\n          {required: true, message: \"联系方式不能为空\", trigger: \"blur\"},\n          {pattern: /^((0\\d{2,3}\\d{5,8})|(1[3-9]\\d{9}))$/, message: \"请输入正确的联系方式\", trigger: \"blur\"},\n        ],\n      },\n      // 默认密码\n      initPassword: null,\n    };\n  },\n  watch: {\n    value: {\n      handler(newVal, oldVal) {\n        this.handleValue(newVal);\n      },\n      deep: true,\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.selected = this.multiple ? [] : '';\n      this.handleValue(this.value);\n    });\n  },\n  methods: {\n    // 处理值\n    handleValue(value) {\n      if (typeof value !== 'undefined' && value !== null && value !== '') {\n        if (this.multiple) {\n          let selected = Array.isArray(value) ? value : value.split(this.separator);\n          if (selected.length > 0) this.selected = selected.map(item => parseInt(item));\n        } else {\n          this.selected = parseInt(value);\n        }\n        this.handleSelected(this.selected);\n      } else {\n        this.selected = this.multiple ? [] : '';\n      }\n    },\n    // 处理选中项\n    handleSelected(value) {\n      if (typeof value !== 'undefined' || value !== null) {\n        if (this.multiple) {\n          // this.options = [];\n          if (value.length > 0) {\n            //只有在初始化时进行查询\n            if (this.options.length <= 0) {\n              getUsers(value).then(response => {\n                  // 处理数据\n                  const newOptions = response.data.map(item => ({\n                    userId: item.userId,\n                    nickName: item.nickName,\n                    phonenumber: item.phonenumber\n                  }));\n                  // 避免重复添加数据\n                  const uniqueOptions = newOptions.filter(option => !this.options.some(existingOption => existingOption.userId === option.userId));\n                  // 更新 options\n                  this.options.push(...uniqueOptions);\n                  // 更新 ids\n                  const newIds = uniqueOptions.map(option => option.userId);\n                  this.ids.push(...newIds);\n                })\n            }\n\n            // value.forEach(item => {\n            //   getUser(item).then(response => {\n            //     this.options.push(response.data);\n            //   });\n            // });\n          }\n        } else {\n          if (!this.options.some(item => item[this.idName] === value)) {\n            getUser(value).then(response => {\n              this.options = [response.data];\n            });\n          }\n        }\n      } else {\n        this.ids = [];\n        this.options = [];\n      }\n    },\n    // 处理清空事件\n    handleClear() {\n      this.ids = [];\n      this.options = [];\n      if (Array.isArray(this.value)) {\n        this.$emit('input', []);\n      } else {\n        this.$emit('input', '');\n      }\n    },\n    // 处理多选模式下移除tag事件\n    handleRemoveTag(value) {\n      //删除id\n      this.ids.some((item, index) => {\n        if (item === value) {\n          this.ids.splice(index, 1);\n        }\n      });\n      //删除option\n      this.options.some((item, index) => {\n        if (item.userId === value) this.options.splice(index, 1);\n      });\n      if (Array.isArray(this.value)) {\n        this.$emit('input', this.selected);\n      } else {\n        this.$emit('input', this.selected.join(this.separator));\n      }\n    },\n    /** 打开对话框 */\n    openDialog() {\n      this.queryParams.nickName = null;\n      this.queryParams.phonenumber = null;\n      this.queryParams.pageNum = 1;\n      this.queryParams.pageSize = 10;\n      this.getList();\n      // this.getConfigKey(\"sys.user.initPassword\").then(response => {\n      //   this.initPassword = response.msg;\n      // });\n      this.visible = true;\n      this.$refs.elSelect.blur();\n    },\n    /** 关闭对话框 */\n    closeDialog() {\n      this.visible = false;\n    },\n    /** 关闭前的回调，会暂停对话框的关闭 */\n    handleBeforeClose(done) {\n      this.$nextTick(()=>{\n        //关闭后，将选择框全部重置\n        this.ids = [];\n        this.options = [];\n        this.handleValue(this.value);\n        done();\n      })\n    },\n    /** 对话框打开的回调 */\n    handleOpen() {\n    },\n    /** 对话框关闭的回调 */\n    handleClose() {\n    },\n    /** 确认按钮 */\n    handleConfirm() {\n      if (this.multiple) {\n        if (Array.isArray(this.value)) {\n          this.$emit('input', this.ids);\n        } else {\n          this.$emit('input', this.ids.join(this.separator));\n        }\n      } else {\n        let id = this.ids[0];\n        let user = this.list.find(item => item[this.idName] === id);\n        this.options = [user];\n        this.selected = id;\n        this.$emit('setPhone', user.phonenumber);\n        this.$emit('input', id);\n      }\n      this.visible = false;\n      // }\n      // if (this.ids.length == 0){\n      //   this.$message.warning(\"请选择人员！\");\n      // }\n    },\n    /** 取消按钮 */\n    handleCancel() {\n      this.ids = [];\n      this.options = [];\n      this.handleValue(this.value);\n      this.visible = false;\n    },\n    /** 查询列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.noIds = [999999];\n      listUser(this.queryParams).then(response => {\n        this.list = response.rows;\n        this.total = response.total;\n        this.$nextTick(() => {\n          for (let rowdata of this.list) {\n            for (let resdata of this.options) {\n              // if (this.userdata==null){\n              //   this.options=[];\n              // }\n              if (rowdata.userId == resdata.userId) {\n                this.$refs.table.toggleRowSelection(rowdata, true);\n              }\n            }\n          }\n        });\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 处理选择框事件\n    handleSelect(selection, row) {\n      this.$nextTick(() => {\n        if (!this.multiple) {\n          if (selection.length > 1) {\n            this.$refs.table.clearSelection();\n            this.$refs.table.toggleRowSelection(row, true);\n          }\n        } else {\n          var flag = this.options.some(item => {\n            return item.userId == row.userId\n          })\n          if (!flag) {\n            // 回显数据里没有本条，把这条加进来(选中)\n            var tempOption = {\n              userId: row.userId,\n              nickName: row.nickName,\n              phonenumber: row.phonenumber\n            }\n            this.options.push(tempOption);\n            this.ids.push(row.userId);\n          } else {\n            // 回显数据里有本条，把这条删除(取消选中)\n            this.options.forEach((item, index) => {\n              if (item.userId == row.userId) {\n                this.options.splice(index, 1);\n                this.ids.forEach((userId, index) => {\n                  if (userId == item.userId) {\n                    this.ids.splice(index, 1);\n                  }\n                })\n              }\n            });\n          }\n        }\n      });\n    },\n    // 处理全选框事件\n    handleSelectAll(selection) {\n      if (!this.multiple) {\n        this.$refs.table.clearSelection()\n      } else {\n        //全选事件\n        if (selection.length > 0) {\n          this.list.forEach(item => {\n            //将本页未勾选的进行勾选\n            if (!this.options.some(optionItem => {\n              return item.userId === optionItem.userId\n            })) {\n              this.handleSelect(selection, item);\n            }\n          })\n          //取消全选事件\n        } else {\n          this.list.forEach(item => {\n            //将本页勾选的进行取消\n            if (this.options.some(optionItem => {\n              return item.userId === optionItem.userId\n            })) {\n              this.handleSelect(selection, item);\n            }\n          })\n        }\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item[this.idName]);\n      this.single = selection.length !== 1;\n      this.many = !selection.length;\n    },\n    // 处理行点击事件\n    handleClick(row, column, event) {\n      if (row) {\n        let rows = this.$refs.table.selection;\n        // let selected = rows.find(item => item[this.idName] === row[this.idName]);\n        // if (!this.multiple) {\n        //   this.$refs.table.clearSelection();\n        // }\n        // this.$refs.table.toggleRowSelection(row, selected ? undefined : true);\n        //勾选或者取消勾选\n        this.$refs.table.toggleRowSelection(row, this.options.some(item => {\n          return item.userId == row.userId\n        }) ? undefined : true);\n        this.handleSelect(rows, row);\n      }\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = `添加${this.functionName}信息`;\n      this.form.password = this.initPassword;\n      this.form.deptId = this.$store.state.user.deptId;\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row[this.idName] || this.ids\n      getUser(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = `修改${this.functionName}信息`;\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row[this.idName] || this.ids;\n      this.$modal.confirm(`是否确认删除${this.functionName}编号为\"${ids}\"的数据项？`).then(function () {\n        return delUser(ids);\n      }).then(() => {\n        this.$modal.msgSuccess(\"删除成功\");\n        this.getList();\n      }).catch(() => {\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download(`${this.moduleName}/${this.businessName}/export`, {\n        ...this.queryParams,\n      }, `${this.functionName}_${new Date().getTime()}.xlsx`)\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form[this.idName] != null) {\n            updateUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            // 特殊需求不输入账号和密码就可以添加用户\n            this.form.userName = this.form.phonenumber;\n            addUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 取消按钮 */\n    cancel() {\n      this.ids = [];\n      this.options = [];\n      this.handleValue(this.value);\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        deptId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        password: undefined,\n        phonenumber: undefined,\n        email: undefined,\n        sex: undefined,\n        status: \"0\",\n        remark: undefined,\n        postIds: [],\n        roleIds: []\n      };\n      this.resetForm(\"form\");\n    },\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tags-select-input ::v-deep .el-select__tags {\n  white-space: nowrap;\n  overflow: hidden;\n  flex-flow: nowrap;\n  display:flex;\n  flex-wrap:nowrap;\n}\n.tags-select-input ::v-deep .el-select__tags-text {\n  display: inline-block;\n  max-width: 90px;//设置最大宽度 超出显示...\n  white-space: nowrap;\n  overflow: hidden;\n  flex-flow: nowrap;\n  vertical-align:bottom;\n  text-overflow:ellipsis;\n}\n</style>\n"]}]}