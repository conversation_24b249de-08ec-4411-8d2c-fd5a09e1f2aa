{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\attackViewList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\attackViewList.vue", "mtime": 1756294198419}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_AttackAlarm", "require", "_ruoyi", "_attackStage", "_interopRequireDefault", "_index", "_deptSelect", "_attackStageText", "_<PERSON><PERSON><PERSON>n", "_batchBlock", "_data", "name", "components", "AttackStageText", "DeptSelect", "AttackStage", "DetailInfo", "BatchBlock", "dicts", "props", "propsActiveName", "type", "String", "props<PERSON>ueryP<PERSON><PERSON>", "Object", "default", "currentBtn", "Number", "data", "showHandleBatchDialog", "multiple", "handleRules", "showHandleDialog", "victimIpNumData", "threatenNameDetailsQuery", "count", "showAll", "queryParams", "pageNum", "pageSize", "victimIpNumQueryParams", "hitRulesQueryParams", "rangeTime", "loading", "descLoading", "attackAlarmList", "total", "victimIpNumTotal", "hitRulesTotal", "detailDialog", "<PERSON><PERSON><PERSON><PERSON>", "hostIp", "detailType", "isAsset", "currentAssetData", "dipDrawerVisible", "dipDetailsData", "ids", "dipDrawerLoading", "<PERSON><PERSON><PERSON>", "threatenNameDrawerVisible", "threatenNameDetailsData", "threatenNameDrawerLoading", "blockingDialogVisible", "multipleSelection", "locationOptions", "value", "label", "blockStatusOptions", "riskLevelOptions", "rows", "handleStateOptions", "handleStateOption", "handleForm", "id", "handleDesc", "tagColor", "tagBackgroundColor", "watch", "init", "val", "handlePropsQuery", "computed", "noMore", "disabled", "flexColumn<PERSON>idth", "columnWidth", "prop", "mounted", "query", "$route", "keys", "length", "getRiskLevelOptions", "methods", "load", "_this", "setTimeout", "startTime", "endTime", "alarmLevel", "riskLevel", "handleQuery", "showHandle", "row", "_this2", "_objectSpread2", "$nextTick", "handleState", "assetName", "_this3", "startUpdateTime", "parseTime", "endUpdateTime", "Date", "setHours", "getList", "$refs", "stage", "initAttackStage", "reset<PERSON><PERSON>y", "srcIp", "currentSelectedCard", "_this4", "params", "attackIp", "$emit", "listAttackAlarm", "then", "res", "code", "for<PERSON>ach", "e", "childrenData", "finally", "expandChange", "expandRowKeys", "_this5", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getEventSegTypeList", "$set", "getTime", "stop", "handleExport", "download", "concat", "handleDetail", "assetId", "handleAtcClick", "attackSeg", "handleApplicationTagShow", "applicationList", "result", "openDipDetails", "_this6", "getDipDetails", "updateTime", "dipRenderHeader", "h", "_ref", "column", "$index", "style", "marginLeft", "color", "openThreatenNameDetails", "_this7", "getThreatenNameDetails", "handleBlocking", "_this8", "console", "log", "arr", "push", "$message", "warning", "map", "item", "Array", "from", "Set", "batchBlock", "block_ip", "join", "handleSelectionChange", "_this9", "getMulTypeDict", "dictType", "col", "list", "array<PERSON>ength", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "info", "isArray", "Math", "max", "err", "f", "handleStateFormatter", "cellValue", "index", "match", "find", "submitHandleForm", "_this10", "handleVuln", "success", "initData", "showHandleBatch", "_toConsumableArray2", "filter", "error", "submitHandleBatchForm", "_this11", "handleBatchVuln"], "sources": ["src/views/frailty/event/component/attackViewList.vue"], "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label-width=\"100px\" label=\"最近告警时间\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"攻击者IP\" prop=\"destIp\">\n                <el-input\n                  v-model=\"queryParams.attackIp\"\n                  placeholder=\"请输入完整的攻击者IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"地理位置\" prop=\"location\">\n                <el-select v-model=\"queryParams.location\" clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in locationOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\"\n                >查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\"\n                >重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"阻断状态\" prop=\"blockStatus\">\n                <el-select v-model=\"queryParams.blockStatus\" clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in blockStatusOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"6\">\n              <el-form-item label=\"告警等级\" prop=\"riskLevel\">\n                <el-select v-model=\"queryParams.riskLevel\" clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in riskLevelOptions\"\n                    :key=\"item.dictValue\"\n                    :label=\"item.dictLabel\"\n                    :value=\"item.dictValue\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\" :style=\"showAll ? { height: 'calc(100% - 248px)' } :{ height: 'calc(100% - 208px)' }\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">攻击者视角列表</span></div>\n          <div style=\"width: 50%; margin-left: 8%\">\n<!--            <attack-stage-text ref=\"stage\"/>-->\n          </div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"showHandleBatch\"\n                >批量处置\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleBlocking\"\n                >批量阻断</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <div class=\"tableContainer\">\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          :data=\"attackAlarmList\"\n          @selection-change=\"handleSelectionChange\"\n          @expand-change=\"expandChange\" >\n          <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n          <el-table-column label=\"攻击者IP\" min-width=\"150\" prop=\"attackIp\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span>{{ scope.row.attackIp }}</span>\n                <img v-if=\"scope.row.isBlocking\" style=\"width: 24px;margin-left: 10px\" src=\"@/assets/images/block.png\" alt=\"\">\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"关联业务系统\" prop=\"businessApplicationList\" width=\"130\">\n            <template slot-scope=\"scope\">\n              <el-tooltip placement=\"bottom-end\" effect=\"light\" v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\">\n                <div slot=\"content\">\n                  <div v-for=\"(item,tagIndex) in scope.row.businessApplications\" :key=\"item.assetId\" class=\"overflow-tag\" v-if=\"tagIndex <= 5\">\n                    <el-tag type=\"primary\"><span>{{item.assetName}}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 5\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\"><span>{{handleApplicationTagShow(scope.row.businessApplications)}}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"告警等级\" prop=\"riskLevel\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.alarm_attack_risk_level\" :value=\"scope.row.riskLevel\"/>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"攻击者标签\"\n            prop=\"tags\"\n            :width=\"flexColumnWidth\"\n            :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span class=\"tag-name\" v-for=\"(tag,index) in scope.row.tags\" :style=\"{ backgroundColor: tagBackgroundColor[index], color: tagColor[index], marginRight: scope.row.tags.length > 0 ? '5px' : '0'}\">{{ tag.tagName }}</span>\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"地理位置\" min-width=\"120\" prop=\"location\"  />\n          <el-table-column label=\"攻击目标IP数\" min-width=\"120\" prop=\"victimIpNums\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"text\" @click=\"openDipDetails(scope.row)\">{{scope.row.victimIpNums}}</el-button>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"命中规则数\" min-width=\"120\" prop=\"attackTypeNums\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"text\" @click=\"openThreatenNameDetails(scope.row)\">{{scope.row.attackTypeNums}}</el-button>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"告警数量\" min-width=\"120\" prop=\"attackNums\"  />\n          <el-table-column label=\"最早告警时间\" min-width=\"120\" prop=\"startTime\"  />\n          <el-table-column label=\"最近告警时间\" min-width=\"120\" prop=\"updateTime\"  />\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.synchronizationStatus === '0'\">未同步</span>\n              <span v-else-if=\"scope.row.synchronizationStatus === '1'\">已同步</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"处置状态\" prop=\"handleState\" width=\"120\" :formatter=\"handleStateFormatter\"/>\n          <el-table-column label=\"操作\" width=\"150\" fixed=\"right\" :show-overflow-tooltip=\"false\" class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button v-if=\"!(scope.row.handleState == 1 || scope.row.handleState == 3)\"\n                         size=\"mini\"\n                         type=\"text\"\n                         @click=\"showHandle(scope.row)\"\n                         v-hasPermi=\"['frailty:loophole:edit']\"\n              >处置\n              </el-button>\n              <el-button\n                :disabled=\"!scope.row.attackIp\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n              >详情\n              </el-button>\n              <el-button\n                :disabled=\"!scope.row.attackIp\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleBlocking(scope.row)\"\n              >阻断\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n\n        </div>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"category\">\n          <el-select\n            v-model=\"handleForm.handleState\"\n            placeholder=\"请选择处置状态\"\n          >\n            <el-option\n              v-for=\"dict in handleStateOption\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" maxlength=\"120\" show-word-limit\n                    placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleForm\">确 定</el-button>\n        <el-button @click=\"showHandleDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      title=\"批量处置\"\n      :visible.sync=\"showHandleBatchDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"category\">\n          <el-select\n            v-model=\"handleForm.handleState\"\n            placeholder=\"请选择处置状态\"\n          >\n            <el-option\n              v-for=\"dict in handleStateOption\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" maxlength=\"120\" show-word-limit\n                    placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleBatchForm\">确 定</el-button>\n        <el-button @click=\"showHandleBatchDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"详情\" :visible.sync=\"detailDialog\" width=\"70%\" append-to-body>\n      <detail-info v-if=\"detailDialog\" :host-ip=\"hostIp\" :detail-type=\"detailType\" :is-asset=\"isAsset\" :current-asset-data=\"currentAssetData\" />\n    </el-dialog>\n\n<!--    <el-drawer\n      :visible.sync=\"dipDrawerVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <el-table :data=\"dipDetailsData\" v-loading=\"dipDrawerLoading\">\n        <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>\n        <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n        <el-table-column property=\"dip\" label=\"目标IP\" :render-header=\"dipRenderHeader\">\n          <template slot-scope=\"scope\">\n            <span>{{scope.row.dip}}</span>\n            <span style=\"color: #ADADAD;margin-left: 5px;\" v-if=\"scope.row.serverName\">[{{scope.row.serverName}}]</span>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-drawer>-->\n\n    <el-dialog\n      :visible.sync=\"dipDrawerVisible\"\n      @close=\"victimIpNumTotal = 0\"\n      width=\"60%\" height=\"50%\" class=\"dip-dialog\">\n      <el-table :data=\"dipDetailsData\" v-loading=\"dipDrawerLoading\">\n<!--         <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>-->\n         <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n         <el-table-column property=\"dip\" label=\"目标IP\" :render-header=\"dipRenderHeader\">\n           <template slot-scope=\"scope\">\n             <span>{{scope.row.dip}}</span>\n             <span style=\"color: #ADADAD;margin-left: 5px;\" v-if=\"scope.row.serverName\">[{{scope.row.serverName}}]</span>\n           </template>\n         </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"victimIpNumTotal>0\"\n        :total=\"victimIpNumTotal\"\n        :page.sync=\"victimIpNumQueryParams.pageNum\"\n        :limit.sync=\"victimIpNumQueryParams.pageSize\"\n        @pagination=\"openDipDetails(victimIpNumData)\"\n      />\n    </el-dialog>\n\n    <el-dialog\n      :visible.sync=\"threatenNameDrawerVisible\"\n      @close=\"hitRulesTotal = 0\"\n      width=\"60%\" height=\"50%\" class=\"dip-dialog\">\n      <el-table :data=\"threatenNameDetailsData\" v-loading=\"threatenNameDrawerLoading\">\n<!--        <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>-->\n        <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n        <el-table-column property=\"threatenName\" label=\"[规则ID] 告警名称\">\n          <template slot-scope=\"scope\">\n            <span :class=\"scope.row.alarmLevel?scope.row.alarmLevel===4?'threatenName-error':scope.row.alarmLevel===3?'threatenName-warn':scope.row.alarmLevel===2?'threatenName-success':'':''\">{{scope.row.threatenName}}</span>\n          </template>\n        </el-table-column>\n        <el-table-column property=\"threatenType\" label=\"攻击类型\"></el-table-column>\n        <el-table-column property=\"count\" label=\"告警数量\"></el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"hitRulesTotal>0\"\n        :total=\"hitRulesTotal\"\n        :page.sync=\"hitRulesQueryParams.pageNum\"\n        :limit.sync=\"hitRulesQueryParams.pageSize\"\n        @pagination=\"openThreatenNameDetails(threatenNameDetailsQuery)\"\n      />\n    </el-dialog>\n\n<!--    <el-drawer\n      :visible.sync=\"threatenNameDrawerVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <el-table :data=\"threatenNameDetailsData\" v-loading=\"threatenNameDrawerLoading\">\n        <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>\n        <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n        <el-table-column property=\"threatenName\" label=\"[规则ID] 告警名称\">\n          <template slot-scope=\"scope\">\n            <span :class=\"scope.row.alarmLevel?scope.row.alarmLevel===4?'threatenName-error':scope.row.alarmLevel===3?'threatenName-warn':scope.row.alarmLevel===2?'threatenName-success':'':''\">{{scope.row.threatenName}}</span>\n          </template>\n        </el-table-column>\n        <el-table-column property=\"threatenType\" label=\"攻击类型\"></el-table-column>\n        <el-table-column property=\"count\" label=\"告警数量\"></el-table-column>\n      </el-table>\n    </el-drawer>-->\n\n    <batch-block :visible.sync=\"blockingDialogVisible\" ref=\"batchBlock\" />\n  </div>\n</template>\n\n<script>\nimport { listAttackAlarm,getDipDetails,getThreatenNameDetails,handleVuln,handleBatchVuln } from \"@/api/threaten/AttackAlarm\";\nimport { parseTime } from \"../../../../utils/ruoyi\";\nimport AttackStage from \"../../../threat/overview/attackStage\";\nimport DetailInfo from \"./attack_detail/index.vue\"\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport AttackStageText from '@/views/threat/overview/attackStageText.vue'\nimport {addBlockIp} from \"@/api/threaten/threatenWarn\";\nimport BatchBlock from \"@/views/frailty/event/component/batchBlock.vue\";\nimport {getMulTypeDict} from \"@/api/system/dict/data\";\n\nexport default {\n  name: \"attackViewList\",\n  components: { AttackStageText, DeptSelect, AttackStage, DetailInfo,BatchBlock },\n  dicts: ['alarm_attack_risk_level'],\n  props: {\n    propsActiveName: {\n      type: String\n    },\n    propsQueryParams: {\n      type: Object,\n      default: function () {\n        return null\n      }\n    },\n    currentBtn: {\n      type: Number,\n      default: null\n    }\n  },\n  data() {\n    return {\n      showHandleBatchDialog: false,\n      multiple: true,\n      handleRules: {},\n      showHandleDialog: false,\n      victimIpNumData: {},\n      threatenNameDetailsQuery:{},\n      count: 10,\n      showAll: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      victimIpNumQueryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      hitRulesQueryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      rangeTime: null,\n      loading: false,\n      descLoading: false,\n      attackAlarmList: [],\n      total: 0,\n      victimIpNumTotal: 0,\n      hitRulesTotal: 0,\n      detailDialog: false,\n      descKey: 0,\n      hostIp: '',\n      detailType: 'attack',\n      isAsset: false,\n      currentAssetData: {},\n      dipDrawerVisible: false,\n      dipDetailsData: [],\n      ids: [],\n      dipDrawerLoading: false,\n      threatenName: 1,\n      threatenNameDrawerVisible: false,\n      threatenNameDetailsData: [],\n      threatenNameDrawerLoading: false,\n      blockingDialogVisible: false,\n      multipleSelection: [],\n      locationOptions: [\n        {value: '1',label: '内网'},\n        {value: '2',label: '外网'},\n        {value: '3',label: '内/外网'},\n      ],\n      blockStatusOptions: [\n        {value: 1,label: '正在阻断'},\n        {value: 2,label: '曾经阻断'},\n      ],\n      riskLevelOptions: [],\n      rows: [],\n      handleStateOptions: [\n        {\n          label: '未处置',\n          value: '0'\n        },\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        },\n        {\n          label: '处置中',\n          value: '3'\n        }\n      ],\n      handleStateOption: [\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        }\n      ],\n      handleForm: {\n        id: '',\n        handleDesc: ''\n      },\n      tagColor: ['#c86c00','#bf1a1a','#1a2bbf','#901abf','#1a2bbf'],\n      tagBackgroundColor: ['#ff9f1933','#fd828233','#7899e033','#c278e033','#7899e033'],\n    }\n  },\n  watch: {\n    propsActiveName() {\n      this.init()\n    },\n    propsQueryParams(val){\n      this.handlePropsQuery(val);\n    },\n  },\n  computed: {\n    noMore () {\n      return this.count >= 20\n    },\n    disabled () {\n      return this.loading || this.noMore\n    },\n    flexColumnWidth() {\n      return this.columnWidth({prop: 'tags', label: '攻击者标签'}, this.attackAlarmList);\n    }\n  },\n  mounted() {\n    let query = this.$route.query;\n    if(!query || Object.keys(query).length < 1){\n      this.init();\n    }else {\n      this.handlePropsQuery(query);\n    }\n    this.getRiskLevelOptions();\n  },\n  methods: {\n    load () {\n      this.loading = true\n      setTimeout(() => {\n        this.count += 2\n        this.loading = false\n      }, 2000)\n    },\n    handlePropsQuery(val) {\n      if(val && Object.keys(val).length > 0){\n        this.queryParams = val;\n        if(val.startTime && val.endTime){\n          this.rangeTime = [val.startTime, val.endTime];\n        }\n        if(val.alarmLevel){\n          if(val.alarmLevel === '2'){\n            this.queryParams.riskLevel = '1';\n          }else if(val.alarmLevel === '3'){\n            this.queryParams.riskLevel = '2';\n          }else if(val.alarmLevel === '4'){\n            this.queryParams.riskLevel = '3';\n          }else {\n            this.queryParams.riskLevel = '-99';\n          }\n        }else {\n          this.queryParams.riskLevel = null;\n        }\n        this.handleQuery();\n      }\n    },\n    showHandle(row) {\n      this.handleForm = {};\n      this.handleForm = {...row};\n      this.$nextTick(() => {\n        if (this.handleForm.handleState == 0) {\n          this.handleForm.handleState = null\n        }\n        this.handleForm.assetName = ''\n        this.showHandleDialog = true;\n      })\n    },\n    init() {\n      this.handleQuery()\n      //this.getList()\n    },\n    handleQuery() {\n      this.queryParams = {...this.queryParams,...this.propsQueryParams};\n      if(this.queryParams.alarmLevel){\n        if(this.queryParams.alarmLevel === '2'){\n          this.queryParams.riskLevel = '1';\n        }else if(this.queryParams.alarmLevel === '3'){\n          this.queryParams.riskLevel = '2';\n        }else if(this.queryParams.alarmLevel === '4'){\n          this.queryParams.riskLevel = '3';\n        }else {\n          this.queryParams.riskLevel = '-99';\n        }\n      }else {\n        this.queryParams.riskLevel = null;\n      }\n      this.queryParams.pageNum = 1\n      this.queryParams.pageSize = 10\n      if (this.rangeTime !== null) {\n        this.queryParams.startUpdateTime = parseTime(this.rangeTime[0]);\n        this.queryParams.endUpdateTime = parseTime(this.rangeTime[1]);\n      } else {\n        this.queryParams.startUpdateTime = null;\n        this.queryParams.endUpdateTime = null;\n      }\n\n      if(!this.queryParams.startUpdateTime){\n        this.queryParams.startUpdateTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'); // 一周前，时间部分为 00:00:00\n      }\n      if(!this.queryParams.endUpdateTime){\n        this.queryParams.endUpdateTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'); // 当前日期，时间部分为 23:59:59\n      }\n      this.rangeTime = [this.queryParams.startUpdateTime, this.queryParams.endUpdateTime];\n      this.getList()\n      this.$nextTick(() => {\n        this.$refs.stage && this.$refs.stage.initAttackStage({\n          ...this.queryParams,\n          startTime: this.queryParams.startUpdateTime,\n          endTime: this.queryParams.endUpdateTime\n        })\n      })\n    },\n    resetQuery() {\n      this.queryParams = {\n        srcIp: '',\n        alarmLevel: ''\n      }\n      this.propsQueryParams.riskLevel = this.queryParams.riskLevel;\n      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel;\n      if(this.$refs.stage){\n        this.$refs.stage.currentSelectedCard = null;\n      }\n      this.rangeTime = []\n      this.handleQuery();\n    },\n    getList() {\n      this.loading = true;\n      //同步请求类型统计数据\n      let params = {...this.queryParams};\n      params.srcIp = this.queryParams.attackIp;\n      params.startTime = this.queryParams.startUpdateTime;\n      params.endTime = this.queryParams.endUpdateTime;\n      if(params.riskLevel){\n        if(params.riskLevel === '1'){\n          params.alarmLevel = '2';\n        }else if(params.riskLevel === '2'){\n          params.alarmLevel = '3';\n        }else if(params.riskLevel === '3'){\n          params.alarmLevel = '4';\n        }\n      }\n      this.$emit('getList',params);\n      listAttackAlarm(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.attackAlarmList = res.rows\n          this.attackAlarmList.forEach(e => {\n            e.childrenData = {}\n          })\n          this.total = res.total\n        }\n      }).finally(()=>{\n        this.loading = false;\n      })\n    },\n    async expandChange(row, expandRowKeys) {\n      if (expandRowKeys.length > 0) {\n        getEventSegTypeList({\n          attackIp: row.attackIp,\n          startTime: this.queryParams.startUpdateTime,\n          endTime: this.queryParams.endUpdateTime\n        }).then(res => {\n          if (res.code === 200) {\n            const childrenData = res.data\n            this.$set(row, 'childrenData', childrenData)\n            this.descKey = new Date().getTime()\n          }\n        })\n      }\n    },\n    handleExport() {\n      this.download('/threaten/AttackAlarm/export', {\n        ...this.queryParams\n      }, `攻击者视角数据_${new Date().getTime()}.xlsx`)\n    },\n    handleDetail(row) {\n      if (row.attackIp) {\n        this.detailDialog = true\n        this.hostIp = row.attackIp;\n        this.isAsset = row.assetId;\n        this.currentAssetData = row;\n      }\n    },\n    handleAtcClick(attackSeg){\n      this.queryParams.attackSeg = attackSeg;\n      this.handleQuery();\n    },\n    handleApplicationTagShow(applicationList){\n      if(!applicationList || applicationList.length < 1){\n        return '';\n      }\n      let result = applicationList[0].assetName;\n      if(applicationList.length > 1){\n        result += '...';\n      }\n      return result;\n    },\n    openDipDetails(row){\n      // this.victimIpNumTotal = 0 ;\n        this.dipDetailsData = [];\n      this.dipDrawerVisible = true;\n      this.dipDrawerLoading = true;\n      this.victimIpNumData = row;\n      getDipDetails({\n        attackIp: row.attackIp,\n        startTime: row.startTime,\n        updateTime: row.updateTime,\n        id: row.id,\n        pageSize: this.victimIpNumQueryParams.pageSize,\n        pageNum: this.victimIpNumQueryParams.pageNum\n      }).then(res => {\n        this.dipDetailsData = res.data.rows;\n        this.victimIpNumTotal = res.data.total;\n      }).finally(() => {\n        this.dipDrawerLoading = false;\n      })\n    },\n    dipRenderHeader(h, { column, $index }){\n      return h('div', [\n        h('span', column.label),\n        h('span', {\n          style: {\n            marginLeft: '5px',\n            color: '#ADADAD'\n          }\n        }, '[主机名称]')\n      ])\n    },\n    openThreatenNameDetails(row){\n      this.threatenNameDetailsData = [];\n      this.threatenNameDrawerVisible = true;\n      this.threatenNameDrawerLoading = true;\n      this.threatenNameDetailsQuery = row\n      getThreatenNameDetails({\n        attackIp: row.attackIp,\n        startTime: row.startTime,\n        updateTime: row.updateTime,\n        id: row.id,\n        pageSize: this.hitRulesQueryParams.pageSize,\n        pageNum: this.hitRulesQueryParams.pageNum\n      }).then(res => {\n        this.threatenNameDetailsData = res.data.rows;\n        this.hitRulesTotal = res.data.total;\n      }).finally(() => {\n        this.threatenNameDrawerLoading = false;\n      })\n    },\n    handleBlocking(row) {\n      console.log( row)\n      let arr = [];\n      if(row && row.attackIp){\n        arr.push(row.attackIp);\n      }else {\n        if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip');\n        arr = this.multipleSelection.map(item => item.attackIp);\n        arr = Array.from(new Set(arr));\n      }\n      this.blockingDialogVisible = true;\n      this.$nextTick(() => {\n        this.$refs.batchBlock && this.$refs.batchBlock.init({\n          block_ip: arr.join(';')\n        })\n      })\n    },\n    // 多选\n    handleSelectionChange(val) {\n      this.ids = val.map(item => item.id);\n      this.multiple = !val.length;\n      this.multipleSelection = val;\n      this.rows = val;\n    },\n    getRiskLevelOptions(){\n      getMulTypeDict({\n        dictType: 'alarm_attack_risk_level'\n      }).then(res => {\n        this.riskLevelOptions = res.data;\n      })\n    },\n    columnWidth(col, list) {\n      // 获取数组长度作为基础\n      let arrayLength = 0;\n\n      // 遍历数据列表，找出 tags 数组的最大长度\n      for (let info of list) {\n        if (info[col.prop] && Array.isArray(info[col.prop])) {\n          arrayLength = Math.max(arrayLength, info[col.prop].length);\n        }\n      }\n\n      // 设置最小宽度为140px\n      return Math.max(arrayLength * 140, 140);\n    },\n    handleStateFormatter(row, column, cellValue, index) {\n      let name = '未处置';\n      let match = this.handleStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    submitHandleForm() {\n      handleVuln(this.handleForm).then(res => {\n        this.$message.success(\"处置成功\");\n        this.handleForm = {};\n        this.showHandleDialog = false;\n        this.getList();\n        this.initData();\n      })\n    },\n    showHandleBatch() {\n      let rows = [...this.rows];\n      rows = rows.filter(item => item.handleState == 0 || item.handleState == 2 || item.handleState === null);\n      if (rows.length < this.rows.length) {\n        this.$message.error('选择中有已处置或处置中事件，无法批量处置');\n        return false;\n      }\n      this.handleForm = {};\n      if (rows.length === 1) {\n        if (rows[0].handleState == 2) {\n          this.handleForm = rows[0]\n        }\n      }\n      this.handleForm.ids = this.ids;\n      this.showHandleBatchDialog = true;\n    },\n\n    submitHandleBatchForm() {\n      handleBatchVuln(this.handleForm).then(res => {\n        this.$message.success(\"处置成功\");\n        this.handleForm = {};\n        this.showHandleBatchDialog = false;\n        this.getList();\n        this.initData();\n      })\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.asset-tag{\n  max-width: 100%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n.dip-dialog{\n  ::v-deep .el-dialog{\n\n  }\n  ::v-deep .el-dialog__body{\n    padding: 20px;\n    margin-top: 20px;\n  }\n}\n.attack-tag:not(:nth-child(-n+2)) {\n  margin-top: 5px;\n}\n.overflow-tag:not(:first-child){\n  margin-top: 5px;\n}\n.threatenName-error{\n  color: #F56C6C;\n}\n.threatenName-success{\n  color: #67C23A;\n}\n.threatenName-warn{\n  color: #E6A23C;\n}\n\n.tag-name {\n  display: inline-block;\n  padding: 2px 12px;\n  margin-bottom: 5px;\n  font-size: 14px;\n  font-weight: normal;\n  line-height: 1.42857143;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  background-image: none;\n  border: 1px solid transparent;\n  border-radius: 4px;\n}\n\n::v-deep .el-dialog__body {\n  max-height: 80vh;\n  padding: 0 30px 30px;\n  overflow-y: auto;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiYA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,MAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,WAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,gBAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,aAAA,GAAAP,OAAA;AACA,IAAAQ,WAAA,GAAAL,sBAAA,CAAAH,OAAA;AACA,IAAAS,KAAA,GAAAT,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAU,IAAA;EACAC,UAAA;IAAAC,eAAA,EAAAA,wBAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,UAAA,EAAAA,cAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,KAAA;IACAC,eAAA;MACAC,IAAA,EAAAC;IACA;IACAC,gBAAA;MACAF,IAAA,EAAAG,MAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAC,UAAA;MACAL,IAAA,EAAAM,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,qBAAA;MACAC,QAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,wBAAA;MACAC,KAAA;MACAC,OAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,sBAAA;QACAF,OAAA;QACAC,QAAA;MACA;MACAE,mBAAA;QACAH,OAAA;QACAC,QAAA;MACA;MACAG,SAAA;MACAC,OAAA;MACAC,WAAA;MACAC,eAAA;MACAC,KAAA;MACAC,gBAAA;MACAC,aAAA;MACAC,YAAA;MACAC,OAAA;MACAC,MAAA;MACAC,UAAA;MACAC,OAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,cAAA;MACAC,GAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,yBAAA;MACAC,uBAAA;MACAC,yBAAA;MACAC,qBAAA;MACAC,iBAAA;MACAC,eAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,kBAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAE,gBAAA;MACAC,IAAA;MACAC,kBAAA,GACA;QACAJ,KAAA;QACAD,KAAA;MACA,GACA;QACAC,KAAA;QACAD,KAAA;MACA,GACA;QACAC,KAAA;QACAD,KAAA;MACA,GACA;QACAC,KAAA;QACAD,KAAA;MACA,EACA;MACAM,iBAAA,GACA;QACAL,KAAA;QACAD,KAAA;MACA,GACA;QACAC,KAAA;QACAD,KAAA;MACA,EACA;MACAO,UAAA;QACAC,EAAA;QACAC,UAAA;MACA;MACAC,QAAA;MACAC,kBAAA;IACA;EACA;EACAC,KAAA;IACA1D,eAAA,WAAAA,gBAAA;MACA,KAAA2D,IAAA;IACA;IACAxD,gBAAA,WAAAA,iBAAAyD,GAAA;MACA,KAAAC,gBAAA,CAAAD,GAAA;IACA;EACA;EACAE,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,YAAAhD,KAAA;IACA;IACAiD,QAAA,WAAAA,SAAA;MACA,YAAAzC,OAAA,SAAAwC,MAAA;IACA;IACAE,eAAA,WAAAA,gBAAA;MACA,YAAAC,WAAA;QAAAC,IAAA;QAAApB,KAAA;MAAA,QAAAtB,eAAA;IACA;EACA;EACA2C,OAAA,WAAAA,QAAA;IACA,IAAAC,KAAA,QAAAC,MAAA,CAAAD,KAAA;IACA,KAAAA,KAAA,IAAAjE,MAAA,CAAAmE,IAAA,CAAAF,KAAA,EAAAG,MAAA;MACA,KAAAb,IAAA;IACA;MACA,KAAAE,gBAAA,CAAAQ,KAAA;IACA;IACA,KAAAI,mBAAA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MAAA,IAAAC,KAAA;MACA,KAAArD,OAAA;MACAsD,UAAA;QACAD,KAAA,CAAA7D,KAAA;QACA6D,KAAA,CAAArD,OAAA;MACA;IACA;IACAsC,gBAAA,WAAAA,iBAAAD,GAAA;MACA,IAAAA,GAAA,IAAAxD,MAAA,CAAAmE,IAAA,CAAAX,GAAA,EAAAY,MAAA;QACA,KAAAvD,WAAA,GAAA2C,GAAA;QACA,IAAAA,GAAA,CAAAkB,SAAA,IAAAlB,GAAA,CAAAmB,OAAA;UACA,KAAAzD,SAAA,IAAAsC,GAAA,CAAAkB,SAAA,EAAAlB,GAAA,CAAAmB,OAAA;QACA;QACA,IAAAnB,GAAA,CAAAoB,UAAA;UACA,IAAApB,GAAA,CAAAoB,UAAA;YACA,KAAA/D,WAAA,CAAAgE,SAAA;UACA,WAAArB,GAAA,CAAAoB,UAAA;YACA,KAAA/D,WAAA,CAAAgE,SAAA;UACA,WAAArB,GAAA,CAAAoB,UAAA;YACA,KAAA/D,WAAA,CAAAgE,SAAA;UACA;YACA,KAAAhE,WAAA,CAAAgE,SAAA;UACA;QACA;UACA,KAAAhE,WAAA,CAAAgE,SAAA;QACA;QACA,KAAAC,WAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAhC,UAAA;MACA,KAAAA,UAAA,OAAAiC,cAAA,CAAAjF,OAAA,MAAA+E,GAAA;MACA,KAAAG,SAAA;QACA,IAAAF,MAAA,CAAAhC,UAAA,CAAAmC,WAAA;UACAH,MAAA,CAAAhC,UAAA,CAAAmC,WAAA;QACA;QACAH,MAAA,CAAAhC,UAAA,CAAAoC,SAAA;QACAJ,MAAA,CAAAzE,gBAAA;MACA;IACA;IACA+C,IAAA,WAAAA,KAAA;MACA,KAAAuB,WAAA;MACA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAQ,MAAA;MACA,KAAAzE,WAAA,OAAAqE,cAAA,CAAAjF,OAAA,MAAAiF,cAAA,CAAAjF,OAAA,WAAAY,WAAA,QAAAd,gBAAA;MACA,SAAAc,WAAA,CAAA+D,UAAA;QACA,SAAA/D,WAAA,CAAA+D,UAAA;UACA,KAAA/D,WAAA,CAAAgE,SAAA;QACA,gBAAAhE,WAAA,CAAA+D,UAAA;UACA,KAAA/D,WAAA,CAAAgE,SAAA;QACA,gBAAAhE,WAAA,CAAA+D,UAAA;UACA,KAAA/D,WAAA,CAAAgE,SAAA;QACA;UACA,KAAAhE,WAAA,CAAAgE,SAAA;QACA;MACA;QACA,KAAAhE,WAAA,CAAAgE,SAAA;MACA;MACA,KAAAhE,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAE,QAAA;MACA,SAAAG,SAAA;QACA,KAAAL,WAAA,CAAA0E,eAAA,OAAAC,gBAAA,OAAAtE,SAAA;QACA,KAAAL,WAAA,CAAA4E,aAAA,OAAAD,gBAAA,OAAAtE,SAAA;MACA;QACA,KAAAL,WAAA,CAAA0E,eAAA;QACA,KAAA1E,WAAA,CAAA4E,aAAA;MACA;MAEA,UAAA5E,WAAA,CAAA0E,eAAA;QACA,KAAA1E,WAAA,CAAA0E,eAAA,OAAAC,gBAAA,MAAAE,IAAA,GAAAC,QAAA;MACA;MACA,UAAA9E,WAAA,CAAA4E,aAAA;QACA,KAAA5E,WAAA,CAAA4E,aAAA,OAAAD,gBAAA,MAAAE,IAAA,GAAAC,QAAA;MACA;MACA,KAAAzE,SAAA,SAAAL,WAAA,CAAA0E,eAAA,OAAA1E,WAAA,CAAA4E,aAAA;MACA,KAAAG,OAAA;MACA,KAAAT,SAAA;QACAG,MAAA,CAAAO,KAAA,CAAAC,KAAA,IAAAR,MAAA,CAAAO,KAAA,CAAAC,KAAA,CAAAC,eAAA,KAAAb,cAAA,CAAAjF,OAAA,MAAAiF,cAAA,CAAAjF,OAAA,MACAqF,MAAA,CAAAzE,WAAA;UACA6D,SAAA,EAAAY,MAAA,CAAAzE,WAAA,CAAA0E,eAAA;UACAZ,OAAA,EAAAW,MAAA,CAAAzE,WAAA,CAAA4E;QAAA,EACA;MACA;IACA;IACAO,UAAA,WAAAA,WAAA;MACA,KAAAnF,WAAA;QACAoF,KAAA;QACArB,UAAA;MACA;MACA,KAAA7E,gBAAA,CAAA8E,SAAA,QAAAhE,WAAA,CAAAgE,SAAA;MACA,KAAA9E,gBAAA,CAAA6E,UAAA,QAAA/D,WAAA,CAAA+D,UAAA;MACA,SAAAiB,KAAA,CAAAC,KAAA;QACA,KAAAD,KAAA,CAAAC,KAAA,CAAAI,mBAAA;MACA;MACA,KAAAhF,SAAA;MACA,KAAA4D,WAAA;IACA;IACAc,OAAA,WAAAA,QAAA;MAAA,IAAAO,MAAA;MACA,KAAAhF,OAAA;MACA;MACA,IAAAiF,MAAA,OAAAlB,cAAA,CAAAjF,OAAA,WAAAY,WAAA;MACAuF,MAAA,CAAAH,KAAA,QAAApF,WAAA,CAAAwF,QAAA;MACAD,MAAA,CAAA1B,SAAA,QAAA7D,WAAA,CAAA0E,eAAA;MACAa,MAAA,CAAAzB,OAAA,QAAA9D,WAAA,CAAA4E,aAAA;MACA,IAAAW,MAAA,CAAAvB,SAAA;QACA,IAAAuB,MAAA,CAAAvB,SAAA;UACAuB,MAAA,CAAAxB,UAAA;QACA,WAAAwB,MAAA,CAAAvB,SAAA;UACAuB,MAAA,CAAAxB,UAAA;QACA,WAAAwB,MAAA,CAAAvB,SAAA;UACAuB,MAAA,CAAAxB,UAAA;QACA;MACA;MACA,KAAA0B,KAAA,YAAAF,MAAA;MACA,IAAAG,4BAAA,OAAA1F,WAAA,EAAA2F,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAP,MAAA,CAAA9E,eAAA,GAAAoF,GAAA,CAAA3D,IAAA;UACAqD,MAAA,CAAA9E,eAAA,CAAAsF,OAAA,WAAAC,CAAA;YACAA,CAAA,CAAAC,YAAA;UACA;UACAV,MAAA,CAAA7E,KAAA,GAAAmF,GAAA,CAAAnF,KAAA;QACA;MACA,GAAAwF,OAAA;QACAX,MAAA,CAAAhF,OAAA;MACA;IACA;IACA4F,YAAA,WAAAA,aAAA/B,GAAA,EAAAgC,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAjH,OAAA,mBAAAkH,oBAAA,CAAAlH,OAAA,IAAAmH,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAlH,OAAA,IAAAqH,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACA,IAAAV,aAAA,CAAA5C,MAAA;gBACAuD,mBAAA;kBACAtB,QAAA,EAAArB,GAAA,CAAAqB,QAAA;kBACA3B,SAAA,EAAAuC,MAAA,CAAApG,WAAA,CAAA0E,eAAA;kBACAZ,OAAA,EAAAsC,MAAA,CAAApG,WAAA,CAAA4E;gBACA,GAAAe,IAAA,WAAAC,GAAA;kBACA,IAAAA,GAAA,CAAAC,IAAA;oBACA,IAAAG,YAAA,GAAAJ,GAAA,CAAArG,IAAA;oBACA6G,MAAA,CAAAW,IAAA,CAAA5C,GAAA,kBAAA6B,YAAA;oBACAI,MAAA,CAAAvF,OAAA,OAAAgE,IAAA,GAAAmC,OAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAL,QAAA,CAAAM,IAAA;UAAA;QAAA,GAAAT,OAAA;MAAA;IACA;IACAU,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,qCAAA9C,cAAA,CAAAjF,OAAA,MACA,KAAAY,WAAA,iDAAAoH,MAAA,CACA,IAAAvC,IAAA,GAAAmC,OAAA;IACA;IACAK,YAAA,WAAAA,aAAAlD,GAAA;MACA,IAAAA,GAAA,CAAAqB,QAAA;QACA,KAAA5E,YAAA;QACA,KAAAE,MAAA,GAAAqD,GAAA,CAAAqB,QAAA;QACA,KAAAxE,OAAA,GAAAmD,GAAA,CAAAmD,OAAA;QACA,KAAArG,gBAAA,GAAAkD,GAAA;MACA;IACA;IACAoD,cAAA,WAAAA,eAAAC,SAAA;MACA,KAAAxH,WAAA,CAAAwH,SAAA,GAAAA,SAAA;MACA,KAAAvD,WAAA;IACA;IACAwD,wBAAA,WAAAA,yBAAAC,eAAA;MACA,KAAAA,eAAA,IAAAA,eAAA,CAAAnE,MAAA;QACA;MACA;MACA,IAAAoE,MAAA,GAAAD,eAAA,IAAAlD,SAAA;MACA,IAAAkD,eAAA,CAAAnE,MAAA;QACAoE,MAAA;MACA;MACA,OAAAA,MAAA;IACA;IACAC,cAAA,WAAAA,eAAAzD,GAAA;MAAA,IAAA0D,MAAA;MACA;MACA,KAAA1G,cAAA;MACA,KAAAD,gBAAA;MACA,KAAAG,gBAAA;MACA,KAAAzB,eAAA,GAAAuE,GAAA;MACA,IAAA2D,0BAAA;QACAtC,QAAA,EAAArB,GAAA,CAAAqB,QAAA;QACA3B,SAAA,EAAAM,GAAA,CAAAN,SAAA;QACAkE,UAAA,EAAA5D,GAAA,CAAA4D,UAAA;QACA1F,EAAA,EAAA8B,GAAA,CAAA9B,EAAA;QACAnC,QAAA,OAAAC,sBAAA,CAAAD,QAAA;QACAD,OAAA,OAAAE,sBAAA,CAAAF;MACA,GAAA0F,IAAA,WAAAC,GAAA;QACAiC,MAAA,CAAA1G,cAAA,GAAAyE,GAAA,CAAArG,IAAA,CAAA0C,IAAA;QACA4F,MAAA,CAAAnH,gBAAA,GAAAkF,GAAA,CAAArG,IAAA,CAAAkB,KAAA;MACA,GAAAwF,OAAA;QACA4B,MAAA,CAAAxG,gBAAA;MACA;IACA;IACA2G,eAAA,WAAAA,gBAAAC,CAAA,EAAAC,IAAA;MAAA,IAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA;QAAAC,MAAA,GAAAF,IAAA,CAAAE,MAAA;MACA,OAAAH,CAAA,SACAA,CAAA,SAAAE,MAAA,CAAArG,KAAA,GACAmG,CAAA;QACAI,KAAA;UACAC,UAAA;UACAC,KAAA;QACA;MACA,aACA;IACA;IACAC,uBAAA,WAAAA,wBAAArE,GAAA;MAAA,IAAAsE,MAAA;MACA,KAAAjH,uBAAA;MACA,KAAAD,yBAAA;MACA,KAAAE,yBAAA;MACA,KAAA5B,wBAAA,GAAAsE,GAAA;MACA,IAAAuE,mCAAA;QACAlD,QAAA,EAAArB,GAAA,CAAAqB,QAAA;QACA3B,SAAA,EAAAM,GAAA,CAAAN,SAAA;QACAkE,UAAA,EAAA5D,GAAA,CAAA4D,UAAA;QACA1F,EAAA,EAAA8B,GAAA,CAAA9B,EAAA;QACAnC,QAAA,OAAAE,mBAAA,CAAAF,QAAA;QACAD,OAAA,OAAAG,mBAAA,CAAAH;MACA,GAAA0F,IAAA,WAAAC,GAAA;QACA6C,MAAA,CAAAjH,uBAAA,GAAAoE,GAAA,CAAArG,IAAA,CAAA0C,IAAA;QACAwG,MAAA,CAAA9H,aAAA,GAAAiF,GAAA,CAAArG,IAAA,CAAAkB,KAAA;MACA,GAAAwF,OAAA;QACAwC,MAAA,CAAAhH,yBAAA;MACA;IACA;IACAkH,cAAA,WAAAA,eAAAxE,GAAA;MAAA,IAAAyE,MAAA;MACAC,OAAA,CAAAC,GAAA,CAAA3E,GAAA;MACA,IAAA4E,GAAA;MACA,IAAA5E,GAAA,IAAAA,GAAA,CAAAqB,QAAA;QACAuD,GAAA,CAAAC,IAAA,CAAA7E,GAAA,CAAAqB,QAAA;MACA;QACA,SAAA7D,iBAAA,CAAA4B,MAAA,kBAAA0F,QAAA,CAAAC,OAAA;QACAH,GAAA,QAAApH,iBAAA,CAAAwH,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAA5D,QAAA;QAAA;QACAuD,GAAA,GAAAM,KAAA,CAAAC,IAAA,KAAAC,GAAA,CAAAR,GAAA;MACA;MACA,KAAArH,qBAAA;MACA,KAAA4C,SAAA;QACAsE,MAAA,CAAA5D,KAAA,CAAAwE,UAAA,IAAAZ,MAAA,CAAA5D,KAAA,CAAAwE,UAAA,CAAA9G,IAAA;UACA+G,QAAA,EAAAV,GAAA,CAAAW,IAAA;QACA;MACA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAhH,GAAA;MACA,KAAAvB,GAAA,GAAAuB,GAAA,CAAAwG,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA/G,EAAA;MAAA;MACA,KAAA5C,QAAA,IAAAkD,GAAA,CAAAY,MAAA;MACA,KAAA5B,iBAAA,GAAAgB,GAAA;MACA,KAAAV,IAAA,GAAAU,GAAA;IACA;IACAa,mBAAA,WAAAA,oBAAA;MAAA,IAAAoG,MAAA;MACA,IAAAC,oBAAA;QACAC,QAAA;MACA,GAAAnE,IAAA,WAAAC,GAAA;QACAgE,MAAA,CAAA5H,gBAAA,GAAA4D,GAAA,CAAArG,IAAA;MACA;IACA;IACA0D,WAAA,WAAAA,YAAA8G,GAAA,EAAAC,IAAA;MACA;MACA,IAAAC,WAAA;;MAEA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAA/K,OAAA,EACA4K,IAAA;QAAAI,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAvI,KAAA;UACA,IAAA2I,IAAA,CAAAT,GAAA,CAAA7G,IAAA,KAAAmG,KAAA,CAAAoB,OAAA,CAAAD,IAAA,CAAAT,GAAA,CAAA7G,IAAA;YACA+G,WAAA,GAAAS,IAAA,CAAAC,GAAA,CAAAV,WAAA,EAAAO,IAAA,CAAAT,GAAA,CAAA7G,IAAA,EAAAK,MAAA;UACA;QACA;;QAEA;MAAA,SAAAqH,GAAA;QAAAV,SAAA,CAAAnE,CAAA,CAAA6E,GAAA;MAAA;QAAAV,SAAA,CAAAW,CAAA;MAAA;MACA,OAAAH,IAAA,CAAAC,GAAA,CAAAV,WAAA;IACA;IACAa,oBAAA,WAAAA,qBAAA3G,GAAA,EAAAgE,MAAA,EAAA4C,SAAA,EAAAC,KAAA;MACA,IAAA1M,IAAA;MACA,IAAA2M,KAAA,QAAA/I,kBAAA,CAAAgJ,IAAA,WAAA9B,IAAA;QAAA,OAAAA,IAAA,CAAAvH,KAAA,IAAAkJ,SAAA;MAAA;MACA,IAAAE,KAAA;QACA3M,IAAA,GAAA2M,KAAA,CAAAnJ,KAAA;MACA;MACA,OAAAxD,IAAA;IACA;IACA6M,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,uBAAA,OAAAjJ,UAAA,EAAAuD,IAAA,WAAAC,GAAA;QACAwF,OAAA,CAAAnC,QAAA,CAAAqC,OAAA;QACAF,OAAA,CAAAhJ,UAAA;QACAgJ,OAAA,CAAAzL,gBAAA;QACAyL,OAAA,CAAArG,OAAA;QACAqG,OAAA,CAAAG,QAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,IAAAvJ,IAAA,OAAAwJ,mBAAA,CAAArM,OAAA,OAAA6C,IAAA;MACAA,IAAA,GAAAA,IAAA,CAAAyJ,MAAA,WAAAtC,IAAA;QAAA,OAAAA,IAAA,CAAA7E,WAAA,SAAA6E,IAAA,CAAA7E,WAAA,SAAA6E,IAAA,CAAA7E,WAAA;MAAA;MACA,IAAAtC,IAAA,CAAAsB,MAAA,QAAAtB,IAAA,CAAAsB,MAAA;QACA,KAAA0F,QAAA,CAAA0C,KAAA;QACA;MACA;MACA,KAAAvJ,UAAA;MACA,IAAAH,IAAA,CAAAsB,MAAA;QACA,IAAAtB,IAAA,IAAAsC,WAAA;UACA,KAAAnC,UAAA,GAAAH,IAAA;QACA;MACA;MACA,KAAAG,UAAA,CAAAhB,GAAA,QAAAA,GAAA;MACA,KAAA5B,qBAAA;IACA;IAEAoM,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,4BAAA,OAAA1J,UAAA,EAAAuD,IAAA,WAAAC,GAAA;QACAiG,OAAA,CAAA5C,QAAA,CAAAqC,OAAA;QACAO,OAAA,CAAAzJ,UAAA;QACAyJ,OAAA,CAAArM,qBAAA;QACAqM,OAAA,CAAA9G,OAAA;QACA8G,OAAA,CAAAN,QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}