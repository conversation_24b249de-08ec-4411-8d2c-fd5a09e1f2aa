package com.ruoyi.threaten.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.safe.domain.FfsafeIpfilterBlocking;
import com.ruoyi.safe.domain.NetworkIpMacInfo;
import com.ruoyi.safe.domain.TblBusinessApplication;
import com.ruoyi.safe.service.IFfsafeIpfilterBlockingService;
import com.ruoyi.safe.service.ITblBusinessApplicationService;
import com.ruoyi.safe.service.ITblNetworkIpMacService;
import com.ruoyi.threaten.domain.TblAttackAlarm;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import com.ruoyi.threaten.service.ITblAttackAlarmService;
import com.ruoyi.work.domain.TblWorkOrder;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 攻击者视角告警列Controller
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@RestController
@RequestMapping("/threaten/AttackAlarm")
public class TblAttackAlarmController extends BaseController
{
    @Autowired
    private ITblAttackAlarmService tblAttackAlarmService;
    @Resource
    private IFfsafeIpfilterBlockingService ffsafeIpfilterBlockingService;
    @Resource
    private ITblNetworkIpMacService netIpMacService;
    @Resource
    private ITblBusinessApplicationService businessApplicationService;

    /**
     * 查询攻击者视角告警列列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TblAttackAlarm tblAttackAlarm)
    {
        //查询netIpMac
        JSONObject paramsData = new JSONObject();
        List<NetworkIpMacInfo> networkIpMacInfos = handleAssetInfo(paramsData);
        startPage();
        List<TblAttackAlarm> list = tblAttackAlarmService.selectTblAttackAlarmList(tblAttackAlarm);
        handleResultAssetInfo(list,networkIpMacInfos);
        handleResultBlocking(list);
        return getDataTable(list);
    }

    /**
     * 导出攻击者视角告警列列表
     */
    @PreAuthorize("@ss.hasPermi('threaten:AttackAlarm:export')")
    @Log(title = "攻击者视角告警列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblAttackAlarm tblAttackAlarm)
    {
        List<TblAttackAlarm> list = tblAttackAlarmService.selectTblAttackAlarmList(tblAttackAlarm);
        ExcelUtil<TblAttackAlarm> util = new ExcelUtil<TblAttackAlarm>(TblAttackAlarm.class);
        util.exportExcel(response, list, "攻击者视角告警列数据");
    }

    /**
     * 获取攻击者视角告警列详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tblAttackAlarmService.selectTblAttackAlarmById(id));
    }

    @GetMapping("/getDipDetails")
    public AjaxResult getDipDetails(TblAttackAlarm tblAttackAlarm){
        if(StrUtil.isBlank(tblAttackAlarm.getAttackIp())){
            return AjaxResult.error("参数异常");
        }
        return AjaxResult.success(tblAttackAlarmService.getDipDetails(tblAttackAlarm));
    }

    @GetMapping("/getThreatenNameDetails")
    public AjaxResult getThreatenNameDetails(TblAttackAlarm tblAttackAlarm){
        if(StrUtil.isBlank(tblAttackAlarm.getAttackIp())){
            return AjaxResult.error("参数异常");
        }
        return AjaxResult.success(tblAttackAlarmService.getThreatenNameDetails(tblAttackAlarm));
    }

    @GetMapping("/groupAlarmLevelStatistics")
    public AjaxResult groupAlarmLevelStatistics(TblAttackAlarm tblAttackAlarm){
        /*JSONObject paramsData = new JSONObject();
        List<NetworkIpMacInfo> networkIpMacInfos = handleAssetInfo(paramsData);*/

        List<JSONObject> dataList = tblAttackAlarmService.groupAlarmLevelStatistics(tblAttackAlarm);
        JSONObject result = new JSONObject();
        dataList.forEach(data -> {
            int curLevel = data.getIntValue("alarm_level",0);
            curLevel++;
            result.put("alarmLevel" + curLevel,data.getLongValue("alarmNum",0));
        });
        Set<String> keySet = result.keySet();
        Long total = 0L;
        for (String key : keySet) {
            total += result.getLong(key);
        }
        result.put("total",total);
        return AjaxResult.success(result);
    }

    /**
     * 新增攻击者视角告警列
     */
    @PreAuthorize("@ss.hasPermi('threaten:AttackAlarm:add')")
    @Log(title = "攻击者视角告警列", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblAttackAlarm tblAttackAlarm)
    {
        return toAjax(tblAttackAlarmService.insertTblAttackAlarm(tblAttackAlarm));
    }

    /**
     * 修改攻击者视角告警列
     */
    @PreAuthorize("@ss.hasPermi('threaten:AttackAlarm:edit')")
    @Log(title = "攻击者视角告警列", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblAttackAlarm tblAttackAlarm)
    {
        return toAjax(tblAttackAlarmService.updateTblAttackAlarm(tblAttackAlarm));
    }

    /**
     * 删除攻击者视角告警列
     */
    @PreAuthorize("@ss.hasPermi('threaten:AttackAlarm:remove')")
    @Log(title = "攻击者视角告警列", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tblAttackAlarmService.deleteTblAttackAlarmByIds(ids));
    }

    private List<NetworkIpMacInfo> handleAssetInfo(JSONObject data){
        //查询netIpMac
        JSONObject queryAssetInfoParams = new JSONObject();
        queryAssetInfoParams.put("domainId",data.getString("domainId"));
        queryAssetInfoParams.put("deptId",data.getLong("deptId"));
        queryAssetInfoParams.put("applicationId",data.getLong("applicationId"));
        List<NetworkIpMacInfo> assetInfoList = netIpMacService.selectAssetInfoList(queryAssetInfoParams);
        if(CollUtil.isNotEmpty(assetInfoList)){
            data.put("ipv4List",assetInfoList.stream().map(NetworkIpMacInfo::getIpv4).collect(Collectors.toList()));
            //查询业务系统
            List<Long> businessAssetIds = new ArrayList<>();
            assetInfoList.forEach(assetInfo -> {
                String assetIdList = assetInfo.getAssetIdList();
                if(StrUtil.isNotBlank(assetIdList)){
                    businessAssetIds.addAll(StrUtil.split(assetIdList, ",").stream().map(Long::valueOf).collect(Collectors.toList()));
                }
            });
            if(CollUtil.isNotEmpty(businessAssetIds)){
                List<TblBusinessApplication> applicationList = businessApplicationService.selectByAssetIds(CollUtil.distinct(businessAssetIds));
                if(CollUtil.isNotEmpty(applicationList)){
                    assetInfoList.forEach(assetInfo -> {
                        String assetIdList = assetInfo.getAssetIdList();
                        if(StrUtil.isNotBlank(assetIdList)){
                            List<TblBusinessApplication> matchAppList = applicationList.stream().filter(application -> assetIdList.contains(application.getAssetId().toString())).collect(Collectors.toList());
                            assetInfo.setBusinessApplications(matchAppList);
                        }
                    });
                }
            }
        }
        boolean isAll = false;
        LoginUser loginUser = getLoginUser();
        if(loginUser != null){
            SysUser user = loginUser.getUser();
            isAll = user.haveAllData();
        }
        if(CollUtil.isEmpty(data.getList("ipv4List",String.class)) && !isAll){
            data.put("ipv4List",CollUtil.toList("noIP"));
        }
        if((data.get("applicationId") == null && data.get("deptId") == null && StrUtil.isBlank(data.getString("domainId"))) && isAll){
            data.remove("ipv4List");
        }
        return assetInfoList;
    }

    private void handleResultAssetInfo(List<TblAttackAlarm> attackAlarmList, List<NetworkIpMacInfo> assetInfoList){
        if(CollUtil.isNotEmpty(attackAlarmList) && CollUtil.isNotEmpty(assetInfoList)){
            attackAlarmList.forEach(attackAlarm -> {
                assetInfoList.stream().filter(assetInfo -> StrUtil.isNotBlank(assetInfo.getIpv4()) && (assetInfo.getIpv4().equals(attackAlarm.getAttackIp())))
                        .findFirst().ifPresent(assetInfo -> {
                            attackAlarm.setBusinessApplications(assetInfo.getBusinessApplications());
                        });
            });
        }
    }

    private void handleResultBlocking(List<TblAttackAlarm> attackAlarmList){
        if(CollUtil.isNotEmpty(attackAlarmList)){
            List<String> ipList = attackAlarmList.stream().map(TblAttackAlarm::getAttackIp).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(ipList)){
                List<FfsafeIpfilterBlocking> list = ffsafeIpfilterBlockingService.list(new LambdaQueryWrapper<FfsafeIpfilterBlocking>()
                        .in(FfsafeIpfilterBlocking::getIp, ipList));
                DateTime nowDate = DateUtil.date();
                attackAlarmList.forEach(attackAlarm -> {
                    list.stream().filter(item -> item.getIp().equals(attackAlarm.getAttackIp())).findFirst().ifPresent(item -> {
                        if(item.getReleaseTime().after(nowDate)){
                            attackAlarm.setIsBlocking(true);
                        }
                    });
                });
            }
        }
    }


    @PostMapping("/handle")
    public AjaxResult handle(@RequestBody TblAttackAlarm attackAlarm){
        attackAlarm.setHandleUser(getUserId());
        attackAlarm.setHandleTime(DateUtils.getNowDate());
        TblAttackAlarm tblAttackAlarm = tblAttackAlarmService.selectTblAttackAlarmById(attackAlarm.getId());
        if(tblAttackAlarm == null){
            return AjaxResult.error("告警不存在");
        }
        int i = tblAttackAlarmService.updateTblAttackAlarm(attackAlarm);
        return AjaxResult.success("处置成功");
    }

    @PostMapping("/handleBatch")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult handleBatch(@RequestBody TblAttackAlarm attackAlarm){
        if (attackAlarm.getIds() == null){
            return AjaxResult.error("请选择要处置的告警");
        }
        attackAlarm.setHandleUser(getUserId());
        attackAlarm.setHandleTime(DateUtils.getNowDate());
        tblAttackAlarmService.updateBatchTblAttackAlarm(attackAlarm);
        return AjaxResult.success("处置成功");
    }
}
