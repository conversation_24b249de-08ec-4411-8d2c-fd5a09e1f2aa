package com.ruoyi.monitor2.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.cron.CronUtil;
import cn.hutool.cron.task.Task;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.monitor2.domain.OperateWorkCreateInfo;
import com.ruoyi.monitor2.domain.TblOperateWorkRecord;
import com.ruoyi.monitor2.service.ITblOperateWorkRecordService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import com.ruoyi.monitor2.mapper.TblOperateWorkMapper;
import com.ruoyi.monitor2.domain.TblOperateWork;
import com.ruoyi.monitor2.service.ITblOperateWorkService;

import javax.annotation.Resource;

/**
 * 事务管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Service
@Slf4j
@EnableAsync
public class TblOperateWorkServiceImpl implements ITblOperateWorkService
{
    @Autowired
    private TblOperateWorkMapper tblOperateWorkMapper;
    @Resource
    private TokenService tokenService;
    @Resource
    private ISysUserService sysUserService;
    @Resource
    private ISysDictDataService dictDataService;
    @Resource
    private ITblOperateWorkRecordService operateWorkRecordService;
    @Value("${server.port}")
    private String port;
    private static final String FLOW_TASK_URL = "/api/workflow/Engine/FlowTask";

    /**
     * 查询事务管理
     *
     * @param id 事务管理主键
     * @return 事务管理
     */
    @Override
    public TblOperateWork selectTblOperateWorkById(Long id)
    {
        return tblOperateWorkMapper.selectTblOperateWorkById(id);
    }

    /**
     * 批量查询事务管理
     *
     * @param ids 事务管理主键集合
     * @return 事务管理集合
     */
    @Override
    public List<TblOperateWork> selectTblOperateWorkByIds(Long[] ids)
    {
        return tblOperateWorkMapper.selectTblOperateWorkByIds(ids);
    }

    /**
     * 查询事务管理列表
     *
     * @param tblOperateWork 事务管理
     * @return 事务管理
     */
    @Override
    public List<TblOperateWork> selectTblOperateWorkList(TblOperateWork tblOperateWork)
    {
        return tblOperateWorkMapper.selectTblOperateWorkList(tblOperateWork);
    }

    /**
     * 新增事务管理
     *
     * @param tblOperateWork 事务管理
     * @return 结果
     */
    @Override
    public int insertTblOperateWork(TblOperateWork tblOperateWork)
    {
        tblOperateWork.setCreateTime(DateUtils.getNowDate());
        return tblOperateWorkMapper.insertTblOperateWork(tblOperateWork);
    }

    /**
     * 修改事务管理
     *
     * @param tblOperateWork 事务管理
     * @return 结果
     */
    @Override
    public int updateTblOperateWork(TblOperateWork tblOperateWork)
    {
        tblOperateWork.setUpdateTime(DateUtils.getNowDate());
        return tblOperateWorkMapper.updateTblOperateWork(tblOperateWork);
    }

    /**
     * 删除事务管理信息
     *
     * @param id 事务管理主键
     * @return 结果
     */
    @Override
    public int deleteTblOperateWorkById(Long id)
    {
        return tblOperateWorkMapper.deleteTblOperateWorkById(id);
    }

    /**
     * 批量删除事务管理
     *
     * @param ids 需要删除的事务管理主键
     * @return 结果
     */
    @Override
    public int deleteTblOperateWorkByIds(Long[] ids)
    {
        return tblOperateWorkMapper.deleteTblOperateWorkByIds(ids);
    }

    @Override
    public List<TblOperateWork> selectTblOperateWorkByName(TblOperateWork operateWork) {
        return tblOperateWorkMapper.selectTblOperateWorkByName(operateWork);
    }

    /**
     * 处理cron任务
     * @param id
     */
    @Override
    @Async
    public void handleCronTask(Long id) {
        TblOperateWork operateWork = selectTblOperateWorkById(id);
        beforeStartOpenTask(operateWork);
    }

    @Override
    @Async
    public void handleCronTask(List<Long> ids) {
        List<TblOperateWork> operateWorkList = selectTblOperateWorkByIds(ids.toArray(new Long[0]));
        operateWorkList.forEach(this::beforeStartOpenTask);
    }

    private void beforeStartOpenTask(TblOperateWork operateWork){
        if(operateWork == null){
            return;
        }
        if(operateWork.getWorkType() != 1L){
            //非待办任务
            return;
        }
        if(operateWork.getPeriodType() == null || StrUtil.isBlank(operateWork.getPeriodTime())){
            //周期未设置
            return;
        }
        String taskId = Constants.OPERATE_WORK_TASK + operateWork.getId();
        if(operateWork.getStatus() == 0L){
            //关闭
            CronUtil.remove(taskId);
            log.info("事务管理-移除任务:{}",taskId);
        }else if(operateWork.getStatus() == 1L){
            //开启
            startTask(operateWork);
        }
    }

    private void startTask(TblOperateWork operateWork){
        String cron = getCron(operateWork);
        if(StrUtil.isBlank(cron)){
            log.error("事务管理-开启任务-获取cron失败:{}", operateWork);
            return;
        }
        String taskId = Constants.OPERATE_WORK_TASK + operateWork.getId();
        Task task = CronUtil.getScheduler().getTask(taskId);
        if(task != null){
            log.info("事务管理-移除任务:{}",taskId);
            CronUtil.remove(taskId);
        }
        if(StrUtil.isBlank(operateWork.getFlowTemplateId())){
            log.error("事务管理-开启任务-获取流程模板失败:{}", operateWork);
            return;
        }
        log.info("事务管理-开启任务:{},cron:{}",operateWork,cron);
        CronUtil.schedule(taskId, cron, () -> {
            log.info("事务管理-执行任务:{},cron:{}",operateWork,cron);
            createFlowTask(operateWork);
            log.info("事务管理-执行任务完成:{}",operateWork);
        });
    }

    private String getCron(TblOperateWork operateWork){
        Long periodType = operateWork.getPeriodType(); //1=每天 2=每周 3=每月
        List<String> periodDate = operateWork.getPeriodDate();
        String periodTime = operateWork.getPeriodTime();
        List<String> cronItemList = new ArrayList<>();
        DateTime dateTime = DateUtil.parse(periodTime, "HH:mm:ss");
        int hour = dateTime.hour(true);
        int minute = dateTime.minute();
        int second = dateTime.second();
        cronItemList.add(String.valueOf(second)); //秒
        cronItemList.add(String.valueOf(minute)); //分
        cronItemList.add(String.valueOf(hour)); //时
        //日
        if(periodType != 3L){
            //不是指定某日
            cronItemList.add("*");
        }else {
            //指定某日
            if(CollUtil.isEmpty(periodDate)){
                //没有指定日期
                return null;
            }
            cronItemList.add(CollUtil.join(periodDate, ","));
        }
        cronItemList.add("*"); //月
        //周
        if(periodType != 2L){
            cronItemList.add("?");
        }else {
            //计算周
            if(CollUtil.isEmpty(periodDate)){
                //没有指定星期
                return null;
            }
            cronItemList.add(CollUtil.join(periodDate, ","));
        }


        return CollUtil.join(cronItemList, " ");
    }

    @Override
    public String createFlowTask(TblOperateWork operateWork){
        log.info("事务管理-发起流程任务:{}",operateWork);
        if(operateWork.getWorkClass() == null){
            log.error("创建事务出错: 事务分类为空，可能是由于绑定的流程不正确----{}", operateWork);
            return null;
        }
        String personId = operateWork.getPersonId();
        if(StrUtil.isBlank(personId)){
            personId = "1";
        }
        SysUser sysUser = sysUserService.selectUserById(Long.valueOf(personId));
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(sysUser);
        loginUser.setLoginTime(DateUtil.date().getTime());
        loginUser.setUserId(sysUser.getUserId());
        loginUser.setDeptId(sysUser.getDeptId());
        String token = tokenService.createToken(loginUser);
        //发起请求
        String getFlowInfoUrl = StrUtil.format("http://127.0.0.1:{}/proxy/api/workflow/Engine/flowTemplate/{}",port,operateWork.getFlowTemplateId());
        String getFlowInfoRes = HttpRequest.get(getFlowInfoUrl).header("Authorization", token).execute().body();
        Object startNodeObj = JSON.parseObject(getFlowInfoRes).getJSONObject("data").getJSONArray("flowTemplateJson").get(0);
        if(startNodeObj == null){
            throw new ServiceException("未找到流程节点");
        }
        JSONObject startNode = BeanUtil.toBean(startNodeObj, JSONObject.class);
        String flowId = startNode.getString("flowId");
        if(StrUtil.isBlank(flowId)){
            throw new ServiceException("流程ID为空");
        }
        JSONObject flowModel = new JSONObject();
        flowModel.put("flowId", flowId);
        flowModel.put("status", -1);
        flowModel.put("flowUrgent",operateWork.getPersonId());
        flowModel.put("isCustom",true);
        //查询事务form
        JSONObject formData = selectOperateWorkSrcColumnById(operateWork.getId());
        if(formData == null){
            throw new ServiceException("事务表单为空");
        }
        if(StrUtil.isBlank(formData.getString("work_class"))){
            throw new ServiceException("事务发起---事务字段不正确");
        }
        formData.put("work_id", operateWork.getId());
        formData.remove("id");
        flowModel.put("formData",formData);
        //检查合法性
        String checkUrl = StrUtil.format("http://127.0.0.1:{}/proxy/api/workflow/Engine/FlowTask/getFormTableInfo",port);
        String checkBody = HttpRequest.post(checkUrl).header("Authorization", token).body(flowModel.toJSONString()).execute().body();
        JSONObject checkRes = JSONObject.parseObject(checkBody);
        if(checkRes.getIntValue("code") != 200){
            throw new ServiceException("检查流程失败:" + checkRes.getString("msg"));
        }
        JSONArray tables = checkRes.getJSONArray("data");
        if(CollUtil.isEmpty(tables)){
            throw new ServiceException("创建流程失败: 可能事务配置的表单不正确");
        }
        boolean anyMatch = tables.stream().anyMatch(table -> "tbl_operate_work_record".equals(table.toString()));
        if(!anyMatch){
            throw new ServiceException("创建流程失败: 可能事务配置的表单不正确");
        }

        String url = StrUtil.format("http://127.0.0.1:{}/proxy/api/workflow/Engine/FlowTask",port);
        String resBody = HttpRequest.post(url).header("Authorization", token).body(flowModel.toJSONString()).execute().body();
        JSONObject res = JSON.parseObject(resBody);
        int code = res.getIntValue("code");
        if(code != 200){
            throw new ServiceException("创建流程失败:" + res.getString("msg"));
        }
        TblOperateWorkRecord saveRecord = BeanUtil.copyProperties(operateWork, TblOperateWorkRecord.class);
        saveRecord.setId(null);
        saveRecord.setDeptId(sysUser.getDeptId());
        saveRecord.setCreateBy(operateWork.getPersonId());
        saveRecord.setFFlowstate(-1);
        saveRecord.setFHandleUser(Collections.singletonList(operateWork.getPersonId()));
        saveRecord.setCreateTime(DateUtil.date());
        saveRecord.setWorkTitle(operateWork.getWorkName() + "待办");
        saveRecord.setWorkId(operateWork.getId());
        JSONObject data = res.getJSONObject("data");
        if(data != null){
            JSONObject resData = data.getJSONObject("resData");
            if(resData != null){
                JSONObject flowTask = resData.getJSONObject("flowTask");
                if(flowTask != null){
                    String flowTaskId = flowTask.getString("id");
                    if(StrUtil.isNotBlank(flowTaskId)){
                        saveRecord.setFFlowtaskid(flowTaskId);
                        int i = operateWorkRecordService.updateByFlowTaskId(saveRecord);
                        if(i < 1){
                            //更新失败，删除记录
                            String delUrl = StrUtil.format("http://127.0.0.1:{}/proxy{}/delTask/{}",port,FLOW_TASK_URL,flowTaskId);
                            String delBody = HttpRequest.delete(delUrl).header("Authorization", token).body(new JSONObject().toString()).execute().body();
                            log.info("删除流程:" + delBody);
                        }
                        return flowTaskId;
                    }
                }
            }
        }

        return null;
    }

    @Override
    public String createWorkFlowTask(TblOperateWork operateWork){
        log.info("事务管理-发起work流程任务:{}",operateWork);
        if(operateWork.getWorkClass() == null){
            log.error("创建事务出错: 事务分类为空，可能是由于绑定的流程不正确----{}", operateWork);
            return null;
        }
        String personId = operateWork.getPersonId();
        if(StrUtil.isBlank(personId)){
            personId = "1";
        }
        SysUser sysUser = sysUserService.selectUserById(Long.valueOf(personId));
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(sysUser);
        loginUser.setLoginTime(DateUtil.date().getTime());
        loginUser.setUserId(sysUser.getUserId());
        loginUser.setDeptId(sysUser.getDeptId());
        String token = tokenService.createToken(loginUser);
        //发起请求
        String getFlowInfoUrl = StrUtil.format("http://127.0.0.1:{}/proxy/api/workflow/Engine/flowTemplate/{}",port,operateWork.getFlowTemplateId());
        String getFlowInfoRes = HttpRequest.get(getFlowInfoUrl).header("Authorization", token).execute().body();
        Object startNodeObj = JSON.parseObject(getFlowInfoRes).getJSONObject("data").getJSONArray("flowTemplateJson").get(0);
        if(startNodeObj == null){
            throw new ServiceException("未找到流程节点");
        }
        JSONObject startNode = BeanUtil.toBean(startNodeObj, JSONObject.class);
        String flowId = startNode.getString("flowId");
        if(StrUtil.isBlank(flowId)){
            throw new ServiceException("流程ID为空");
        }
        JSONObject flowModel = new JSONObject();
        flowModel.put("flowId", flowId);
        flowModel.put("status", -1);
        flowModel.put("flowUrgent",operateWork.getPersonId());
        flowModel.put("isCustom",true);
        //查询事务form
        JSONObject formData = selectOperateWorkSrcColumnById(operateWork.getId());
        if(formData == null){
            throw new ServiceException("事务表单为空");
        }
        if(StrUtil.isBlank(formData.getString("work_class"))){
            throw new ServiceException("事务发起---事务字段不正确");
        }
        formData.put("work_id", operateWork.getId());
        formData.remove("id");
        flowModel.put("formData",formData);
        //检查合法性
        String checkUrl = StrUtil.format("http://127.0.0.1:{}/proxy/api/workflow/Engine/FlowTask/getFormTableInfo",port);
        String checkBody = HttpRequest.post(checkUrl).header("Authorization", token).body(flowModel.toJSONString()).execute().body();
        JSONObject checkRes = JSONObject.parseObject(checkBody);
        if(checkRes.getIntValue("code") != 200){
            throw new ServiceException("检查流程失败:" + checkRes.getString("msg"));
        }
        JSONArray tables = checkRes.getJSONArray("data");
        if(CollUtil.isEmpty(tables)){
            throw new ServiceException("创建流程失败: 可能事务配置的表单不正确");
        }
        boolean anyMatch = tables.stream().anyMatch(table -> "tbl_operate_work_record".equals(table.toString()));
        if(!anyMatch){
            throw new ServiceException("创建流程失败: 可能事务配置的表单不正确");
        }

        String url = StrUtil.format("http://127.0.0.1:{}/proxy/api/workflow/Engine/FlowTask",port);
        String resBody = HttpRequest.post(url).header("Authorization", token).body(flowModel.toJSONString()).execute().body();
        JSONObject res = JSON.parseObject(resBody);
        int code = res.getIntValue("code");
        if(code != 200){
            throw new ServiceException("创建流程失败:" + res.getString("msg"));
        }
        TblOperateWorkRecord saveRecord = BeanUtil.copyProperties(operateWork, TblOperateWorkRecord.class);
        saveRecord.setId(null);
        saveRecord.setDeptId(sysUser.getDeptId());
        saveRecord.setCreateBy(operateWork.getPersonId());
        saveRecord.setFFlowstate(-1);
        saveRecord.setFHandleUser(Collections.singletonList(operateWork.getPersonId()));
        saveRecord.setCreateTime(DateUtil.date());
        saveRecord.setWorkTitle(operateWork.getWorkName());
        saveRecord.setWorkId(operateWork.getId());
        JSONObject data = res.getJSONObject("data");
        if(data != null){
            JSONObject resData = data.getJSONObject("resData");
            if(resData != null){
                JSONObject flowTask = resData.getJSONObject("flowTask");
                if(flowTask != null){
                    String flowTaskId = flowTask.getString("id");
                    if(StrUtil.isNotBlank(flowTaskId)){
                        saveRecord.setFFlowtaskid(flowTaskId);
                        int i = operateWorkRecordService.updateByFlowTaskId(saveRecord);
                        if(i < 1){
                            //更新失败，删除记录
                            String delUrl = StrUtil.format("http://127.0.0.1:{}/proxy{}/delTask/{}",port,FLOW_TASK_URL,flowTaskId);
                            String delBody = HttpRequest.delete(delUrl).header("Authorization", token).body(new JSONObject().toString()).execute().body();
                            log.info("删除流程:" + delBody);
                        }
                        return flowTaskId;
                    }
                }
            }
        }

        return null;
    }

    private JSONObject selectOperateWorkSrcColumnById(Long id) {
        return tblOperateWorkMapper.selectOperateWorkSrcColumnById(id);
    }

    @Override
    public List<OperateWorkCreateInfo> getOperateWorkCreateList(TblOperateWork operateWork) {
        List<OperateWorkCreateInfo> operateWorkCreateList = new ArrayList<>();
        //查询分类
        SysDictData selectDictDataParams = new SysDictData();
        selectDictDataParams.setDictType("work_class");
        List<SysDictData> dictDataList = dictDataService.selectDictDataList(selectDictDataParams);
        if(CollUtil.isEmpty(dictDataList)){
            return operateWorkCreateList;
        }
        //查事务列表
        TblOperateWork selectOperateWorkParams = new TblOperateWork();
        selectOperateWorkParams.setStatus(1L);
        if(!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            selectOperateWorkParams.setPersonId(String.valueOf(SecurityUtils.getUserId()));
        }
        selectOperateWorkParams.setWorkName(operateWork.getWorkName());
        List<TblOperateWork> operateWorkList = selectTblOperateWorkList(selectOperateWorkParams);
        if(CollUtil.isEmpty(operateWorkList)){
            return operateWorkCreateList;
        }
        //封装返回
        dictDataList.forEach(dictData -> {
            List<TblOperateWork> matchList = operateWorkList.stream().filter(operateWorkItem -> operateWorkItem.getWorkClass() != null && dictData.getDictValue().equals(String.valueOf(operateWorkItem.getWorkClass())))
                    .collect(Collectors.toList());
            if(CollUtil.isNotEmpty(matchList)){
                OperateWorkCreateInfo createInfo = new OperateWorkCreateInfo();
                createInfo.setWorkClass(dictData.getDictLabel());
                createInfo.setOperateWorkList(matchList);
                operateWorkCreateList.add(createInfo);
            }
        });
        return operateWorkCreateList;
    }

    @Override
    public String createWork(TblOperateWork operateWork) {
        if(operateWork == null || operateWork.getId() == null){
           throw new ServiceException("参数错误");
        }
        TblOperateWork operateWorkInDB = selectTblOperateWorkById(operateWork.getId());
        return this.createFlowTask(operateWorkInDB);
    }

    @Override
    public int count(TblOperateWork operateWork) {
        return tblOperateWorkMapper.count(operateWork);
    }

    public static void main(String[] args) {
        String url = "http://127.0.0.1:8080/proxy/api/workflow/Engine/flowTemplate?currentPage=1&pageSize=20&sort=desc&sidx=&keyword=";
        String body = HttpRequest.get(url).execute().body();
        System.out.println(body);
    }
}
