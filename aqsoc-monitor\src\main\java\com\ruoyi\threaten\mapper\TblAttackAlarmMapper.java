package com.ruoyi.threaten.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.threaten.domain.TblAttackAlarm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 攻击者视角告警列Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Repository
public interface TblAttackAlarmMapper
{
    /**
     * 查询攻击者视角告警列
     *
     * @param id 攻击者视角告警列主键
     * @return 攻击者视角告警列
     */
    public TblAttackAlarm selectTblAttackAlarmById(Long id);

    /**
     * 批量查询攻击者视角告警列
     *
     * @param ids 攻击者视角告警列主键集合
     * @return 攻击者视角告警列集合
     */
    public List<TblAttackAlarm> selectTblAttackAlarmByIds(Long[] ids);

    /**
     * 查询攻击者视角告警列列表
     *
     * @param tblAttackAlarm 攻击者视角告警列
     * @return 攻击者视角告警列集合
     */
    public List<TblAttackAlarm> selectTblAttackAlarmList(TblAttackAlarm tblAttackAlarm);

    /**
     * 新增攻击者视角告警列
     *
     * @param tblAttackAlarm 攻击者视角告警列
     * @return 结果
     */
    public int insertTblAttackAlarm(TblAttackAlarm tblAttackAlarm);

    /**
     * 修改攻击者视角告警列
     *
     * @param tblAttackAlarm 攻击者视角告警列
     * @return 结果
     */
    public int updateTblAttackAlarm(TblAttackAlarm tblAttackAlarm);

    /**
     * 删除攻击者视角告警列
     *
     * @param id 攻击者视角告警列主键
     * @return 结果
     */
    public int deleteTblAttackAlarmById(Long id);

    /**
     * 批量删除攻击者视角告警列
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTblAttackAlarmByIds(Long[] ids);

    void batchInsert(@Param("list") List<TblAttackAlarm> attackAlarmList);

    void batchUpdate(@Param("list") List<TblAttackAlarm> updateList);

    /**
     * 查询未同步的攻击者视角告警列表（过去7天）
     *
     * @param startDate 开始时间
     * @return 未同步的攻击者视角告警列表
     */
    List<TblAttackAlarm> selectNotSyncList(Date startDate);

    List<JSONObject> groupAlarmLevelStatistics(TblAttackAlarm tblAttackAlarm);

    void updateBatchTblAttackAlarm(TblAttackAlarm attackAlarm);
}
