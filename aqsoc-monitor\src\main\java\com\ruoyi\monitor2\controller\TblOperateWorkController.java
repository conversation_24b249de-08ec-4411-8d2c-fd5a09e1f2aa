package com.ruoyi.monitor2.controller;

import java.io.File;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.cron.CronUtil;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.monitor2.domain.OperateWorkCreateInfo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.monitor2.domain.TblOperateWork;
import com.ruoyi.monitor2.service.ITblOperateWorkService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 事务管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RestController
@RequestMapping("/operateWork")
public class TblOperateWorkController extends BaseController
{
    @Autowired
    private ITblOperateWorkService tblOperateWorkService;

    /**
     * 查询事务管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TblOperateWork tblOperateWork)
    {
        if(!tblOperateWork.isQueryAllData()){
            startPage();
        }
        /*Long userId = SecurityUtils.getUserId();
        if(!SecurityUtils.isAdmin(userId)){
            tblOperateWork.setPersonId(String.valueOf(userId));
        }*/
        List<TblOperateWork> list = tblOperateWorkService.selectTblOperateWorkList(tblOperateWork);
        return getDataTable(list);
    }

    /**
     * 导出事务管理列表
     */
    @PreAuthorize("@ss.hasPermi('operateWork:operateWork:export')")
    @Log(title = "事务管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblOperateWork tblOperateWork)
    {
        List<TblOperateWork> list = tblOperateWorkService.selectTblOperateWorkList(tblOperateWork);
        ExcelUtil<TblOperateWork> util = new ExcelUtil<TblOperateWork>(TblOperateWork.class);
        util.exportExcel(response, list, "事务管理数据");
    }

    /**
     * 获取事务管理详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tblOperateWorkService.selectTblOperateWorkById(id));
    }

    /**
     * 新增事务管理
     */
    @PreAuthorize("@ss.hasPermi('operateWork:operateWork:add')")
    @Log(title = "事务管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblOperateWork tblOperateWork)
    {
        return toAjax(tblOperateWorkService.insertTblOperateWork(tblOperateWork));
    }

    /**
     * 修改事务管理
     */
    @PreAuthorize("@ss.hasPermi('operateWork:operateWork:edit')")
    @Log(title = "事务管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblOperateWork tblOperateWork)
    {
        //判断是否可以修改
        TblOperateWork inDB = tblOperateWorkService.selectTblOperateWorkById(tblOperateWork.getId());
        if(inDB == null){
            throw new ServiceException("事务不存在");
        }
        if(inDB.getStatus() == 1){
            throw new ServiceException("事务已开启，不允许修改");
        }
        return toAjax(tblOperateWorkService.updateTblOperateWork(tblOperateWork));
    }

    /**
     * 修改事务管理
     */
    @PreAuthorize("@ss.hasPermi('operateWork:operateWork:edit')")
    @Log(title = "事务管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestBody TblOperateWork tblOperateWork)
    {
        TblOperateWork update = new TblOperateWork();
        update.setId(tblOperateWork.getId());
        update.setStatus(tblOperateWork.getStatus());
        int i = tblOperateWorkService.updateTblOperateWork(update);
        tblOperateWorkService.handleCronTask(tblOperateWork.getId());
        return toAjax(i);
    }

    /**
     * 删除事务管理
     */
    @PreAuthorize("@ss.hasPermi('operateWork:operateWork:remove')")
    @Log(title = "事务管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tblOperateWorkService.deleteTblOperateWorkByIds(ids));
    }

    @GetMapping("/checkWorkName")
    public AjaxResult checkWorkName(TblOperateWork operateWork){
        List<TblOperateWork> list = tblOperateWorkService.selectTblOperateWorkByName(operateWork);
        return AjaxResult.success(CollUtil.isNotEmpty(list));
    }

    /**
     * 获取发起事务列表
     * @return
     */
    @GetMapping("/getOperateWorkCreateList")
    public AjaxResult getOperateWorkCreateList(TblOperateWork operateWork){
        List<OperateWorkCreateInfo> operateWorkCreateList = tblOperateWorkService.getOperateWorkCreateList(operateWork);
        return AjaxResult.success(operateWorkCreateList);
    }

    /**
     * 发起事务
     * @param operateWork
     * @return
     */
    @PostMapping("/createWork")
    public AjaxResult createWork(@RequestBody TblOperateWork operateWork){
        String flowTaskId = tblOperateWorkService.createWork(operateWork);
        if(StrUtil.isBlank(flowTaskId)){
            return AjaxResult.error("发起事务失败,可能是该事物配置的流程不正确");
        }
        return AjaxResult.success("成功",flowTaskId);
    }

    @GetMapping("/count")
    public AjaxResult count(TblOperateWork operateWork){
        int count = tblOperateWorkService.count(operateWork);
        return AjaxResult.success(count);
    }
}
