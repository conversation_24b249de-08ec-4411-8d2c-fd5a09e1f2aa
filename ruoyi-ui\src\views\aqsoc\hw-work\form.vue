<template>
  <el-dialog :title="!dataForm.id ? '新增HW事务' :isCopy?'复制HW事务':'编辑HW事务'"
             :close-on-click-modal="false" append-to-body
             :visible.sync="visible" class="JNPF-dialog JNPF-dialog_center" lock-scroll
             width="800px">
    <el-row :gutter="16" class="">
      <el-form ref="elForm" :model="dataForm" :rules="rules" size="small" label-width="150px" label-position="right">
        <template v-if="!loading">
          <el-col :span="12">
            <jnpf-form-tip-item label="年度" prop="year">
              <el-select v-model="dataForm.year" placeholder="请选择">
                <el-option
                  v-for="item in yearList"
                  :key="item"
                  :label="item"
                  :value="item">
                </el-option>
              </el-select>
            </jnpf-form-tip-item>
          </el-col>
          <el-col :span="12">
            <jnpf-form-tip-item label="所属单位" prop="deptId">
              <dept-select
                v-model="dataForm.deptId"
                is-current
              />
            </jnpf-form-tip-item>
          </el-col>
          <el-col :span="12">
            <jnpf-form-tip-item label="联络人" prop="userId">
              <user-select v-model="dataForm.userId" placeholder="请选择" multiple/>
            </jnpf-form-tip-item>
          </el-col>
          <el-col :span="12">
            <jnpf-form-tip-item label="HW开始时间" prop="hwStart">
              <el-date-picker v-model="dataForm.hwStart" type="datetime"  style="width: 192px;"value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择HW开始时间" clearable>
              </el-date-picker>
            </jnpf-form-tip-item>
          </el-col>
          <el-col :span="12">
            <jnpf-form-tip-item label="HW结束时间" prop="hwEnd">
              <el-date-picker v-model="dataForm.hwEnd" type="datetime" style="width: 192px;" value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择HW结束时间" clearable>
              </el-date-picker>
            </jnpf-form-tip-item>
          </el-col>
        </template>
      </el-form>
    </el-row>
    <span slot="footer" class="dialog-footer">
          <el-button @click="visible = false"> 取 消</el-button>
          <el-button type="primary" @click="dataFormSubmit()" :loading="btnLoading"> 确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {addData, getInfo,copyWork} from '@/api/aqsoc/work-hw/crud'
import {mapGetters} from "vuex";
import UserSelect from '@/views/hhlCode/component/userSelect';
import DeptSelect from "@/views/components/select/deptSelect.vue";

export default {
  components: {DeptSelect, UserSelect},
  props: [],
  data() {
    return {
      dataFormSubmitType: 0,
      continueBtnLoading: false,
      index: 0,
      prevDis: false,
      nextDis: false,
      allList: [],
      visible: false,
      loading: false,
      btnLoading: false,
      currTableConf: {},
      addTableConf: {},
      //可选范围默认值
      ableAll: {},
      tableRows: {},
      currVmodel: "",
      dataForm: {
        id: '',
        templateId: '',
        name: '',
        year: '',
        userId: '',
        hwStart: '',
        hwEnd: '',
        supportOrgs: '',
        supportUsers: '',
        dataJson: '',
      },
      rules:
        {
          year: [{required: true, message: `年度不能为空`, trigger: 'change'}],
          //userId: [{required: true, message: `责任人不能为空`, trigger: 'change'}],
          hwStart: [{required: true, message: `HW开始时间不能为空`, trigger: 'change'}],
          hwEnd: [{required: true, message: `HW结束时间不能为空`, trigger: 'change'}],
          deptId: [{required: true, message: `所属单位不能为空`, trigger: 'change'}],
        },
      typeOptions: [],
      typeProps: {"label": "fullName", "value": "enCode"},
      childIndex: -1,
      yearList:[],
      isEdit: false,
      isCopy: false,
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  watch: {},
  created() {
    this.dataAll()
    // 获取当前年份
    const currentYear = new Date().getFullYear();
    this.yearList= [currentYear - 1, currentYear, currentYear + 1];
  },
  mounted() {
  },
  methods: {
    dataAll() {
      this.gettypeOptions();
    },
    gettypeOptions() {

    },
    init(id, isDetail, allList,isCopy) {
      this.isCopy = isCopy;
      this.dataForm.id = id || 0;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs['elForm'].resetFields();
        if (this.dataForm.id) {
          this.loading = true
          getInfo(this.dataForm.id).then(res => {
            this.dataInfo(res.data)
            this.loading = false
          });
        } else {
          this.clearData()
        }
      });
    },
    // 表单提交
    dataFormSubmit(type) {
      this.dataFormSubmitType = type ? type : 0
      this.$refs['elForm'].validate((valid) => {
        if (valid) {
          this.request()
        }
      })
    },
    request() {
      this.btnLoading = true
      let _data = this.dataList()
      if(this.isCopy){
        copyWork(_data).then(res => {
          this.$message({
            message: res.msg,
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.visible = false
              this.btnLoading = false
              this.$emit('refresh', true)
            }
          })
        })
        return;
      }
      if (!this.dataForm.id) {
        addData(_data).then((res) => {
          this.$message({
            message: res.msg,
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.visible = false
              this.btnLoading = false
              this.$emit('refresh', true)
            }
          })
        }).catch(() => {
          this.btnLoading = false
        })
      } else {
        addData(_data).then((res) => {
          this.$message({
            message: res.msg,
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.visible = false
              this.btnLoading = false
              this.$emit('refresh', true)
            }
          })
        }).catch(() => {
          this.btnLoading = false
          this.continueBtnLoading = false
        })
      }
    },
    clearData() {
      this.dataForm = {
        id: '',
        templateId: '',
        name: '',
        year: '',
        userId: '',
        hwStart: '',
        hwEnd: '',
        supportOrgs: '',
        supportUsers: '',
        dataJson: '',
      }
    },
    dataList() {
      let userIdData = this.dataForm.userId.split(','); // 将字符串按逗号分割成数组
      let uniqueUserIdData = [...new Set(userIdData)]; // 使用 Set 去重
      this.dataForm.userId = uniqueUserIdData.join(','); // 将去重后的数组转换为字符串
      var _data = JSON.parse(JSON.stringify(this.dataForm));
      return _data;
    },
    dataInfo(dataAll) {
      let _dataAll = dataAll
      this.dataForm = _dataAll
      this.isEdit = true
      this.dataAll()
      this.childIndex = -1
    },
  },
}

</script>
