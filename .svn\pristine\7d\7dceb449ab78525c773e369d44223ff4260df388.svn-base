package com.ruoyi.ffsafe.api.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttack;
import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttackDetailVO;
import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttackQueryDto;
import com.ruoyi.ffsafe.api.mapper.FfsafeHostIntrusionAttackMapper;
import com.ruoyi.ffsafe.api.service.IFfsafeHostIntrusionAttackService;

/**
 * 主机入侵攻击事件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
public class FfsafeHostIntrusionAttackServiceImpl implements IFfsafeHostIntrusionAttackService {

    @Autowired
    private FfsafeHostIntrusionAttackMapper ffsafeHostIntrusionAttackMapper;


    /**
     * 查询主机入侵攻击事件
     * 
     * @param id 主机入侵攻击事件主键
     * @return 主机入侵攻击事件
     */
    @Override
    public FfsafeHostIntrusionAttack selectFfsafeHostIntrusionAttackById(Long id) {
        return ffsafeHostIntrusionAttackMapper.selectFfsafeHostIntrusionAttackById(id);
    }

    /**
     * 查询主机入侵攻击事件列表
     * 
     * @param ffsafeHostIntrusionAttack 主机入侵攻击事件
     * @return 主机入侵攻击事件
     */
    @Override
    public List<FfsafeHostIntrusionAttack> selectFfsafeHostIntrusionAttackList(FfsafeHostIntrusionAttack ffsafeHostIntrusionAttack) {
        return ffsafeHostIntrusionAttackMapper.selectFfsafeHostIntrusionAttackList(ffsafeHostIntrusionAttack);
    }

    /**
     * 新增主机入侵攻击事件
     * 
     * @param ffsafeHostIntrusionAttack 主机入侵攻击事件
     * @return 结果
     */
    @Override
    public int insertFfsafeHostIntrusionAttack(FfsafeHostIntrusionAttack ffsafeHostIntrusionAttack) {
        return ffsafeHostIntrusionAttackMapper.insertFfsafeHostIntrusionAttack(ffsafeHostIntrusionAttack);
    }

    /**
     * 修改主机入侵攻击事件
     * 
     * @param ffsafeHostIntrusionAttack 主机入侵攻击事件
     * @return 结果
     */
    @Override
    public int updateFfsafeHostIntrusionAttack(FfsafeHostIntrusionAttack ffsafeHostIntrusionAttack) {
        return ffsafeHostIntrusionAttackMapper.updateFfsafeHostIntrusionAttack(ffsafeHostIntrusionAttack);
    }

    /**
     * 批量删除主机入侵攻击事件
     * 
     * @param ids 需要删除的主机入侵攻击事件主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeHostIntrusionAttackByIds(Long[] ids) {
        return ffsafeHostIntrusionAttackMapper.deleteFfsafeHostIntrusionAttackByIds(ids);
    }

    /**
     * 删除主机入侵攻击事件信息
     * 
     * @param id 主机入侵攻击事件主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeHostIntrusionAttackById(Long id) {
        return ffsafeHostIntrusionAttackMapper.deleteFfsafeHostIntrusionAttackById(id);
    }

    /**
     * 批量插入主机入侵攻击事件
     * 
     * @param list 主机入侵攻击事件列表
     * @return 结果
     */
    @Override
    public int batchInsertFfsafeHostIntrusionAttack(List<FfsafeHostIntrusionAttack> list) {
        return ffsafeHostIntrusionAttackMapper.batchInsertFfsafeHostIntrusionAttack(list);
    }

    /**
     * 更新主机入侵攻击事件排除处置相关
     * 
     * @param ffsafeHostIntrusionAttack 主机入侵攻击事件
     * @return 结果
     */
    @Override
    public int updateFfsafeHostIntrusionAttackWithoutHandle(FfsafeHostIntrusionAttack ffsafeHostIntrusionAttack) {
        return ffsafeHostIntrusionAttackMapper.updateFfsafeHostIntrusionAttackWithoutHandle(ffsafeHostIntrusionAttack);
    }

    /**
     * 根据非凡ID和设备配置ID查询记录
     * 
     * @param ffId 非凡ID
     * @param deviceConfigId 设备配置ID
     * @return 主机入侵攻击事件
     */
    @Override
    public FfsafeHostIntrusionAttack selectByFfIdAndDeviceConfigId(Integer ffId, Long deviceConfigId) {
        return ffsafeHostIntrusionAttackMapper.selectByFfIdAndDeviceConfigId(ffId, deviceConfigId);
    }

    /**
     * 根据多个条件查询匹配的记录
     * 
     * @param entityList 实体列表
     * @return 匹配的记录列表
     */
    @Override
    public List<FfsafeHostIntrusionAttack> selectByMultipleFields(List<FfsafeHostIntrusionAttack> entityList) {
        return ffsafeHostIntrusionAttackMapper.selectByMultipleFields(entityList);
    }

    /**
     * 批量更新处置状态
     *
     * @param eventIds 事件ID列表
     * @param handleState 处置状态
     * @param handleDesc 处置描述
     * @param disposer 处置人
     * @return 结果
     */
    @Override
    public int batchUpdateHandleState(List<Long> eventIds, Integer handleState, String handleDesc, String disposer) {
        return ffsafeHostIntrusionAttackMapper.batchUpdateHandleState(eventIds, handleState, handleDesc, disposer);
    }

    /**
     * 根据查询DTO查询主机入侵攻击事件列表
     *
     * @param queryDto 查询条件DTO
     * @return 主机入侵攻击事件集合
     */
    @Override
    public List<FfsafeHostIntrusionAttack> selectFfsafeHostIntrusionAttackListByQuery(FfsafeHostIntrusionAttackQueryDto queryDto) {
        return ffsafeHostIntrusionAttackMapper.selectFfsafeHostIntrusionAttackListByQuery(queryDto);
    }

    /**
     * 统计满足查询条件的记录总数
     *
     * @param queryDto 查询条件DTO
     * @return 统计结果，格式：{"total": 123}
     */
    @Override
    public Map<String, Object> selectAttackTypeStatistics(FfsafeHostIntrusionAttackQueryDto queryDto) {
        return ffsafeHostIntrusionAttackMapper.selectAttackTypeStatistics(queryDto);
    }

    /**
     * 根据攻击ID查询攻击详情（包含详情表数据）
     *
     * @param attackId 攻击事件ID
     * @return 攻击详情信息
     */
    @Override
    public FfsafeHostIntrusionAttackDetailVO selectAttackDetailByAttackId(Long attackId) {
        return ffsafeHostIntrusionAttackMapper.selectAttackDetailByAttackId(attackId);
    }
}
