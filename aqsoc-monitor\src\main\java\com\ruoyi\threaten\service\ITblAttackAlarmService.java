package com.ruoyi.threaten.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.rabbitmq.service.handler.DeviceMessageHandler;
import com.ruoyi.threaten.domain.TblAttackAlarm;

import java.util.List;

/**
 * 攻击者视角告警列Service接口
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface ITblAttackAlarmService extends DeviceMessageHandler<TblAttackAlarm>
{
    /**
     * 查询攻击者视角告警列
     *
     * @param id 攻击者视角告警列主键
     * @return 攻击者视角告警列
     */
    public TblAttackAlarm selectTblAttackAlarmById(Long id);

    /**
     * 批量查询攻击者视角告警列
     *
     * @param ids 攻击者视角告警列主键集合
     * @return 攻击者视角告警列集合
     */
    public List<TblAttackAlarm> selectTblAttackAlarmByIds(Long[] ids);

    /**
     * 查询攻击者视角告警列列表
     *
     * @param tblAttackAlarm 攻击者视角告警列
     * @return 攻击者视角告警列集合
     */
    public List<TblAttackAlarm> selectTblAttackAlarmList(TblAttackAlarm tblAttackAlarm);

    /**
     * 新增攻击者视角告警列
     *
     * @param tblAttackAlarm 攻击者视角告警列
     * @return 结果
     */
    public int insertTblAttackAlarm(TblAttackAlarm tblAttackAlarm);

    /**
     * 修改攻击者视角告警列
     *
     * @param tblAttackAlarm 攻击者视角告警列
     * @return 结果
     */
    public int updateTblAttackAlarm(TblAttackAlarm tblAttackAlarm);

    /**
     * 删除攻击者视角告警列信息
     *
     * @param id 攻击者视角告警列主键
     * @return 结果
     */
    public int deleteTblAttackAlarmById(Long id);

    /**
     * 批量删除攻击者视角告警列
     *
     * @param ids 需要删除的攻击者视角告警列主键集合
     * @return 结果
     */
    public int deleteTblAttackAlarmByIds(Long[] ids);

    /**
     * 同步攻击者视角告警
     *
     * @param attackAlarmList 攻击者视角告警
     */
    void sync(List<TblAttackAlarm> attackAlarmList);

    JSONObject getDipDetails(TblAttackAlarm tblAttackAlarm);

    JSONObject getThreatenNameDetails(TblAttackAlarm tblAttackAlarm);

    /**
     * 查询未同步的攻击者视角告警列表（过去7天）
     *
     * @return 未同步的攻击者视角告警列表
     */
    List<TblAttackAlarm> selectNotSyncList();

    /**
     * 添加阻断IP
     * @param params 阻断参数
     * @return 处理结果
     */
    AjaxResult addBlockIp(JSONObject params);

    List<JSONObject> groupAlarmLevelStatistics(TblAttackAlarm tblAttackAlarm);

    void updateBatchTblAttackAlarm(TblAttackAlarm attackAlarm);
}
