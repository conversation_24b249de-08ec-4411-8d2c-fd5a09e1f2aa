package cn.anmte.aqsoc.work.service;

import cn.anmte.aqsoc.work.domain.WorkHwTask;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.5.3_dev
 * @description:
 * @date 2025-08-25 16:46
 */
public interface IWorkHwTaskService extends IService<WorkHwTask> {
    /**
     * 查询HW事务任务
     *
     * @param id HW事务任务主键
     * @return HW事务任务
     */
    public WorkHwTask selectWorkHwTaskById(Long id);

    /**
     * 批量查询HW事务任务
     *
     * @param ids HW事务任务主键集合
     * @return HW事务任务集合
     */
    public List<WorkHwTask> selectWorkHwTaskByIds(Long[] ids);

    /**
     * 查询HW事务任务列表
     *
     * @param workHwTask HW事务任务
     * @return HW事务任务集合
     */
    public List<WorkHwTask> selectWorkHwTaskList(WorkHwTask workHwTask);

    /**
     * 新增HW事务任务
     *
     * @param workHwTask HW事务任务
     * @return 结果
     */
    public int insertWorkHwTask(WorkHwTask workHwTask);

    /**
     * 修改HW事务任务
     *
     * @param workHwTask HW事务任务
     * @return 结果
     */
    public int updateWorkHwTask(WorkHwTask workHwTask);

    /**
     * 删除HW事务任务信息
     *
     * @param id HW事务任务主键
     * @return 结果
     */
    public int deleteWorkHwTaskById(Long id);

    /**
     * 批量删除HW事务任务
     *
     * @param ids 需要删除的HW事务任务主键集合
     * @return 结果
     */
    public int deleteWorkHwTaskByIds(Long[] ids);

    List<JSONObject> getStageTree(WorkHwTask workHwTask);

    public void startDelayTask(WorkHwTask workHwTask);
}
