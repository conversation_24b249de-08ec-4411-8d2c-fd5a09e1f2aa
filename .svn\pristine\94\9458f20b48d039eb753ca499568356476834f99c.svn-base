<template>
  <div>
    <el-select
      ref="elSelect"
      v-model="selected"
      :placeholder="$attrs['placeholder']||'请选择'+this.functionName"
      :multiple="multiple"
      :clearable="clearable"
      class="tags-select-input"
      collapse-tags
      @focus="openDialog"
      @clear="handleClear"
      @remove-tag="handleRemoveTag"
      v-bind="$attrs">
      <el-option
        v-for="item in options"
        :key="item[valueName]"
        :label="item[labelName]+'-'+item.phonenumber"
        :value="item[valueName]">
      </el-option>
    </el-select>
    <el-dialog
      :title="'选择'+this.functionName"
      :visible.sync="visible"
      :before-close="handleBeforeClose"
      @open="handleOpen"
      @close="handleClose"
      width="800px"
      top="5vh"
      append-to-body>
      <el-form ref="queryForm" :model="queryParams" :inline="true" v-show="showSearch" label-width="68px">
        <!--<el-form-item label="账号" prop="userName">-->
        <!--  <el-input-->
        <!--    v-model="queryParams.userName"-->
        <!--    placeholder="请输入账号"-->
        <!--    clearable-->
        <!--    @keyup.enter.native="handleQuery"-->
        <!--  />-->
        <!--</el-form-item>-->
        <el-form-item label="姓名" prop="nickName">
          <el-input
            v-model="queryParams.nickName"
            placeholder="请输入姓名"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="phonenumber">
          <el-input
            v-model="queryParams.phonenumber"
            placeholder="请输入联系方式"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!--<el-row :gutter="10" class="mb8">-->
      <!--  <el-col :span="1.5">-->
      <!--    <el-button-->
      <!--      type="primary"-->
      <!--      plain-->
      <!--      icon="el-icon-plus"-->
      <!--      size="mini"-->
      <!--      @click="handleAdd"-->
      <!--      v-hasPermi="['system:user:add']"-->
      <!--    >新增</el-button>-->
      <!--  </el-col>-->
      <!--  <el-col :span="1.5">-->
      <!--    <el-button-->
      <!--      type="success"-->
      <!--      plain-->
      <!--      icon="el-icon-edit"-->
      <!--      size="mini"-->
      <!--      :disabled="single"-->
      <!--      @click="handleUpdate"-->
      <!--      v-hasPermi="['system:user:edit']"-->
      <!--    >修改</el-button>-->
      <!--  </el-col>-->
      <!--  <el-col :span="1.5">-->
      <!--    <el-button-->
      <!--      type="danger"-->
      <!--      plain-->
      <!--      icon="el-icon-delete"-->
      <!--      size="mini"-->
      <!--      :disabled="many"-->
      <!--      @click="handleDelete"-->
      <!--      v-hasPermi="['system:user:remove']"-->
      <!--    >删除</el-button>-->
      <!--  </el-col>-->
      <!--  <el-col :span="1.5">-->
      <!--    <el-button-->
      <!--      type="warning"-->
      <!--      plain-->
      <!--      icon="el-icon-download"-->
      <!--      size="mini"-->
      <!--      @click="handleExport"-->
      <!--      v-hasPermi="['system:user:export']"-->
      <!--    >导出</el-button>-->
      <!--  </el-col>-->
      <!--  <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
      <!--</el-row>-->

      <el-table ref="table" v-loading="loading" :data="list" @select="handleSelect" @select-all="handleSelectAll"
                @selection-change="handleSelectionChange" @row-click="handleClick" height="260px">
        <el-table-column type="selection" width="55" ></el-table-column>
        <el-table-column label="账号" prop="userName" :show-overflow-tooltip="true"/>
        <el-table-column label="姓名" prop="nickName" :show-overflow-tooltip="true"/>
        <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.email ? scope.row.email : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="联系方式" prop="phonenumber" :show-overflow-tooltip="true"/>
        <!--<el-table-column label="状态"  prop="status">-->
        <!--  <template slot-scope="scope">-->
        <!--    <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>-->
        <!--  </template>-->
        <!--</el-table-column>-->
        <!--<el-table-column label="创建时间"  prop="createTime" width="180">-->
        <!--  <template slot-scope="scope">-->
        <!--    <span>{{ parseTime(scope.row.createTime) }}</span>-->
        <!--  </template>-->
        <!--</el-table-column>-->
        <!--        <el-table-column label="操作"  class-name="small-padding fixed-width">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-button-->
        <!--              size="mini"-->
        <!--              type="text"-->
        <!--              icon="el-icon-edit"-->
        <!--              @click="handleUpdate(scope.row)"-->
        <!--              v-hasPermi="['system:user:edit']"-->
        <!--            >修改-->
        <!--            </el-button>-->
        <!--            <el-button-->
        <!--              size="mini"-->
        <!--              type="text"-->
        <!--              icon="el-icon-delete"-->
        <!--              @click="handleDelete(scope.row)"-->
        <!--              v-hasPermi="['system:user:remove']"-->
        <!--            >删除-->
        <!--            </el-button>-->
        <!--          </template>
                </el-table-column>-->
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row type="flex" style="flex-wrap: wrap;">
            <el-col :span="12">
              <el-form-item label="姓名" prop="nickName">
                <el-input v-model="form.nickName" placeholder="请输入姓名" maxlength="30"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="归属部门" prop="deptId">
                <dept-select v-model="form.deptId" is-current/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式" prop="phonenumber">
                <el-input v-model="form.phonenumber" placeholder="请输入联系方式" maxlength="11"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50"/>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >添加{{ functionName }}
        </el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addUser, delUser, getUser, getUsers, listUser, updateUser} from "@/api/system/user";
import DeptSelect from '@/views/components/select/deptSelect';

export default {
  name: "UserSelect",
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  components: {DeptSelect},
  props: {
    userdata: {type: String, default: null,},
    // 值
    value: {required: true},
    // 分隔符
    separator: {type: String, required: false, default: ','},
    // 是否多选
    multiple: {type: Boolean, required: false, default: false},
    // 是否可以清空选项
    clearable: {type: Boolean, required: false, default: true},
  },
  data() {
    return {
      // 主键字段名
      idName: 'userId',
      // 选项标签字段名
      labelName: 'nickName',
      // 选项值字段名
      valueName: 'userId',
      // 模块名
      moduleName: 'system',
      // 业务名
      businessName: 'user',
      // 功能名
      functionName: '人员',
      // 选中项
      selected: null,
      // 所有选中项的id和name
      options: [],
      // 是否显示弹出层
      visible: false,
      // 遮罩层
      loading: true,
      // 所有选中项的id
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      many: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        nickName: null,
        phonenumber: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userName: [
          {required: true, message: "账号不能为空", trigger: "blur"},
          {min: 2, max: 20, message: '账号长度必须介于 2 和 20 之间', trigger: 'blur'},
        ],
        deptId: [
          {required: true, message: "部门不能为空", trigger: "blur"},
        ],
        nickName: [
          {required: true, message: "姓名不能为空", trigger: "blur"},
        ],
        password: [
          {required: true, message: "密码不能为空", trigger: "blur"},
          {min: 5, max: 20, message: '密码长度必须介于 5 和 20 之间', trigger: 'blur'},
        ],
        email: [
          {type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"]},
        ],
        phonenumber: [
          {required: true, message: "联系方式不能为空", trigger: "blur"},
          {pattern: /^((0\d{2,3}\d{5,8})|(1[3-9]\d{9}))$/, message: "请输入正确的联系方式", trigger: "blur"},
        ],
      },
      // 默认密码
      initPassword: null,
    };
  },
  watch: {
    value: {
      handler(newVal, oldVal) {
        this.handleValue(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.selected = this.multiple ? [] : '';
      this.handleValue(this.value);
    });
  },
  methods: {
    // 处理值
    handleValue(value) {
      if (typeof value !== 'undefined' && value !== null && value !== '') {
        if (this.multiple) {
          let selected = Array.isArray(value) ? value : value.split(this.separator);
          if (selected.length > 0) this.selected = selected.map(item => parseInt(item));
        } else {
          this.selected = parseInt(value);
        }
        this.handleSelected(this.selected);
      } else {
        this.selected = this.multiple ? [] : '';
      }
    },
    // 处理选中项
    handleSelected(value) {
      if (typeof value !== 'undefined' || value !== null) {
        if (this.multiple) {
          // this.options = [];
          if (value.length > 0) {
            //只有在初始化时进行查询
            if (this.options.length <= 0) {
              getUsers(value).then(response => {
                  // 处理数据
                  const newOptions = response.data.map(item => ({
                    userId: item.userId,
                    nickName: item.nickName,
                    phonenumber: item.phonenumber
                  }));
                  // 避免重复添加数据
                  const uniqueOptions = newOptions.filter(option => !this.options.some(existingOption => existingOption.userId === option.userId));
                  // 更新 options
                  this.options.push(...uniqueOptions);
                  // 更新 ids
                  const newIds = uniqueOptions.map(option => option.userId);
                  this.ids.push(...newIds);
                })
            }

            // value.forEach(item => {
            //   getUser(item).then(response => {
            //     this.options.push(response.data);
            //   });
            // });
          }
        } else {
          if (!this.options.some(item => item[this.idName] === value)) {
            getUser(value).then(response => {
              this.options = [response.data];
            });
          }
        }
      } else {
        this.ids = [];
        this.options = [];
      }
    },
    // 处理清空事件
    handleClear() {
      this.ids = [];
      this.options = [];
      if (Array.isArray(this.value)) {
        this.$emit('input', []);
      } else {
        this.$emit('input', '');
      }
    },
    // 处理多选模式下移除tag事件
    handleRemoveTag(value) {
      //删除id
      this.ids.some((item, index) => {
        if (item === value) {
          this.ids.splice(index, 1);
        }
      });
      //删除option
      this.options.some((item, index) => {
        if (item.userId === value) this.options.splice(index, 1);
      });
      if (Array.isArray(this.value)) {
        this.$emit('input', this.selected);
      } else {
        this.$emit('input', this.selected.join(this.separator));
      }
    },
    /** 打开对话框 */
    openDialog() {
      this.queryParams.nickName = null;
      this.queryParams.phonenumber = null;
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.getList();
      // this.getConfigKey("sys.user.initPassword").then(response => {
      //   this.initPassword = response.msg;
      // });
      this.visible = true;
      this.$refs.elSelect.blur();
    },
    /** 关闭对话框 */
    closeDialog() {
      this.visible = false;
    },
    /** 关闭前的回调，会暂停对话框的关闭 */
    handleBeforeClose(done) {
      this.$nextTick(()=>{
        //关闭后，将选择框全部重置
        this.ids = [];
        this.options = [];
        this.handleValue(this.value);
        done();
      })
    },
    /** 对话框打开的回调 */
    handleOpen() {
    },
    /** 对话框关闭的回调 */
    handleClose() {
    },
    /** 确认按钮 */
    handleConfirm() {
      if (this.multiple) {
        if (Array.isArray(this.value)) {
          this.$emit('input', this.ids);
        } else {
          this.$emit('input', this.ids.join(this.separator));
        }
      } else {
        let id = this.ids[0];
        let user = this.list.find(item => item[this.idName] === id);
        this.options = [user];
        this.selected = id;
        this.$emit('setPhone', user.phonenumber);
        this.$emit('input', id);
      }
      this.visible = false;
      // }
      // if (this.ids.length == 0){
      //   this.$message.warning("请选择人员！");
      // }
    },
    /** 取消按钮 */
    handleCancel() {
      this.ids = [];
      this.options = [];
      this.handleValue(this.value);
      this.visible = false;
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.queryParams.noIds = [999999];
      listUser(this.queryParams).then(response => {
        this.list = response.rows;
        this.total = response.total;
        this.$nextTick(() => {
          for (let rowdata of this.list) {
            for (let resdata of this.options) {
              // if (this.userdata==null){
              //   this.options=[];
              // }
              if (rowdata.userId == resdata.userId) {
                this.$refs.table.toggleRowSelection(rowdata, true);
              }
            }
          }
        });
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 处理选择框事件
    handleSelect(selection, row) {
      this.$nextTick(() => {
        if (!this.multiple) {
          if (selection.length > 1) {
            this.$refs.table.clearSelection();
            this.$refs.table.toggleRowSelection(row, true);
          }
        } else {
          var flag = this.options.some(item => {
            return item.userId == row.userId
          })
          if (!flag) {
            // 回显数据里没有本条，把这条加进来(选中)
            var tempOption = {
              userId: row.userId,
              nickName: row.nickName,
              phonenumber: row.phonenumber
            }
            this.options.push(tempOption);
            this.ids.push(row.userId);
          } else {
            // 回显数据里有本条，把这条删除(取消选中)
            this.options.forEach((item, index) => {
              if (item.userId == row.userId) {
                this.options.splice(index, 1);
                this.ids.forEach((userId, index) => {
                  if (userId == item.userId) {
                    this.ids.splice(index, 1);
                  }
                })
              }
            });
          }
        }
      });
    },
    // 处理全选框事件
    handleSelectAll(selection) {
      if (!this.multiple) {
        this.$refs.table.clearSelection()
      } else {
        //全选事件
        if (selection.length > 0) {
          this.list.forEach(item => {
            //将本页未勾选的进行勾选
            if (!this.options.some(optionItem => {
              return item.userId === optionItem.userId
            })) {
              this.handleSelect(selection, item);
            }
          })
          //取消全选事件
        } else {
          this.list.forEach(item => {
            //将本页勾选的进行取消
            if (this.options.some(optionItem => {
              return item.userId === optionItem.userId
            })) {
              this.handleSelect(selection, item);
            }
          })
        }
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item[this.idName]);
      this.single = selection.length !== 1;
      this.many = !selection.length;
    },
    // 处理行点击事件
    handleClick(row, column, event) {
      if (row) {
        let rows = this.$refs.table.selection;
        // let selected = rows.find(item => item[this.idName] === row[this.idName]);
        // if (!this.multiple) {
        //   this.$refs.table.clearSelection();
        // }
        // this.$refs.table.toggleRowSelection(row, selected ? undefined : true);
        //勾选或者取消勾选
        this.$refs.table.toggleRowSelection(row, this.options.some(item => {
          return item.userId == row.userId
        }) ? undefined : true);
        this.handleSelect(rows, row);
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = `添加${this.functionName}信息`;
      this.form.password = this.initPassword;
      this.form.deptId = this.$store.state.user.deptId;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row[this.idName] || this.ids
      getUser(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = `修改${this.functionName}信息`;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row[this.idName] || this.ids;
      this.$modal.confirm(`是否确认删除${this.functionName}编号为"${ids}"的数据项？`).then(function () {
        return delUser(ids);
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        this.getList();
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(`${this.moduleName}/${this.businessName}/export`, {
        ...this.queryParams,
      }, `${this.functionName}_${new Date().getTime()}.xlsx`)
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form[this.idName] != null) {
            updateUser(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // 特殊需求不输入账号和密码就可以添加用户
            this.form.userName = this.form.phonenumber;
            addUser(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      this.ids = [];
      this.options = [];
      this.handleValue(this.value);
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: []
      };
      this.resetForm("form");
    },
  },
}
</script>

<style lang="scss" scoped>
.tags-select-input ::v-deep .el-select__tags {
  white-space: nowrap;
  overflow: hidden;
  flex-flow: nowrap;
  display:flex;
  flex-wrap:nowrap;
}
.tags-select-input ::v-deep .el-select__tags-text {
  display: inline-block;
  max-width: 90px;//设置最大宽度 超出显示...
  white-space: nowrap;
  overflow: hidden;
  flex-flow: nowrap;
  vertical-align:bottom;
  text-overflow:ellipsis;
}
</style>
