{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\plan.vue?vue&type=style&index=0&id=f546020a&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\plan.vue", "mtime": 1756294198397}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5tYWluewogIHdpZHRoOiAxMDAlOwoKICAuaGVhZC1ib3h7CiAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgZmxleC1zaHJpbms6IDA7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgcGFkZGluZzogMTVweCAxMHB4IDE1cHg7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICBkaXNwbGF5OiBmbGV4OwoKICAgIC5sZWZ0ewogICAgICBhbGlnbi1jb250ZW50OiBjZW50ZXI7CiAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgZm9udC13ZWlnaHQ6IDcwMDsKICAgIH0KICAgIC5yaWdodHsKICAgICAgZmxleDogMTsKICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgICB9CgogICAgLmJ0bjJ7CiAgICAgIGhlaWdodDogMzJweDsKICAgICAgY29sb3I6ICM2NTZDNzU7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgYm9yZGVyOiAxcHggc29saWQgI2RiZGJkYjsKICAgICAgYmFja2dyb3VuZDogI2YyZjdmNzsKICAgIH0KICB9CgogIC53b3JrLXRhYnN7CiAgICA6OnYtZGVlcCAuZWwtdGFic19faXRlbSB7CiAgICAgIGhlaWdodDogNDhweCAhaW1wb3J0YW50OwogICAgICBsaW5lLWhlaWdodDogNDhweCAhaW1wb3J0YW50OwogICAgICB0ZXh0LWFsaWduOiBsZWZ0ICFpbXBvcnRhbnQ7CiAgICAgIHdpZHRoOiAyODVweCAhaW1wb3J0YW50OwogICAgICBwYWRkaW5nOiAwIDEwcHg7CiAgICB9CiAgICAvKuiuvue9ruesrOS4gOS4quagh+etvumhueS4jeS9v+eUqOmAmueUqOeahOaCrOWBnOWSjOmAieS4reaViOaenCovCiAgICA6OnYtZGVlcCAuZWwtdGFic19faXRlbTpub3QoI3RhYi1zZWFyY2gpOmhvdmVyIHsKICAgICAgY29sb3I6ICMzMzMgIWltcG9ydGFudDsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNSAhaW1wb3J0YW50OwogICAgfQogICAgOjp2LWRlZXAgLmVsLXRhYnNfX2l0ZW06bm90KCN0YWItc2VhcmNoKS5pcy1hY3RpdmUgewogICAgICBsaW5lLWhlaWdodDogNDhweDsKICAgICAgY29sb3I6ICMzMzMzMzM7CiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1OwogICAgfQogICAgLndvcmstdGFicy1sYWJlbCB7CiAgICAgIHBhZGRpbmc6IDAgMTBweDsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["plan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkXA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "plan.vue", "sourceRoot": "src/views/aqsoc/hw-work", "sourcesContent": ["<template>\n  <div class=\"main\">\n    <div class=\"head-box\">\n      <div class=\"left\">\n        {{workInfo.year}}HW计划【{{workInfo.hwStart}} 至 {{workInfo.hwEnd}}】\n      </div>\n      <div class=\"right\">\n        <el-button class=\"btn2\" size=\"small\" @click=\"back\">返回</el-button>\n      </div>\n    </div>\n    <div class=\"custom-container\">\n      <div class=\"custom-tree-container\">\n        <div class=\"head-container\">\n          <el-input\n            v-model=\"queryParams.taskName\"\n            placeholder=\"请输入任务名称\"\n            clearable\n            size=\"medium\"\n            prefix-icon=\"el-icon-search\"\n            style=\"margin-bottom: 15px\"\n            @input=\"search\"\n          />\n        </div>\n        <div class=\"head-container\">\n          <el-tabs tab-position=\"left\" style=\"height: 100%;\" class=\"work-tabs\" @tab-click=\"tabClick\">\n            <el-tab-pane :label=\"tabItem.label\" v-for=\"tabItem in stageList\"><div slot=\"label\" class=\"work-tabs-label\">{{`${tabItem.label}（${tabItem.count}）`}}</div></el-tab-pane>\n          </el-tabs>\n        </div>\n      </div>\n      <div class=\"custom-content-container-right\">\n        <div class=\"custom-content-container\">\n          <div class=\"common-header\">\n            <div><span class=\"common-head-title\">任务列表</span></div>\n            <div class=\"common-head-right\">\n              <el-row :gutter=\"10\">\n                <el-col :span=\"1.5\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"small\"\n                    @click=\"handleAdd\"\n                  >新增</el-button>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"dataList\"\n            ref=\"table\"\n            height=\"100%\">\n            <el-table-column label=\"任务名称\" align=\"center\" prop=\"taskName\"/>\n            <el-table-column label=\"所属阶段\" align=\"center\" prop=\"stageClass\">\n              <template slot-scope=\"scope\">\n                <dict-tag :options=\"dict.type.hw_stage_class\" :value=\"scope.row.stageClass\"/>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"任务内容\" align=\"center\" prop=\"content\" />\n            <el-table-column label=\"任务开始时间\" align=\"center\" prop=\"startTime\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"任务结束时间\" align=\"center\" prop=\"endTime\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"负责人\" align=\"center\" prop=\"manageUserName\"/>\n            <el-table-column label=\"关联事务\" align=\"center\" prop=\"operateWorkName\" />\n            <el-table-column\n              label=\"操作\"\n              fixed=\"right\"\n              :show-overflow-tooltip=\"false\"\n              width=\"160\"\n              class-name=\"small-padding fixed-width\"\n            >\n              <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\n                <el-button size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\">编辑</el-button>\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDelete(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n          />\n        </div>\n      </div>\n    </div>\n\n    <!-- 添加或修改事务管理对话框! -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-row>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"HW阶段\" prop=\"stageClass\">\n              <el-select v-model=\"form.stageClass\" clearable placeholder=\"请选择\">\n                <el-option v-for=\"dict in dict.type.hw_stage_class\"\n                           :key=\"dict.value\" :label=\"dict.label\"\n                           :value=\"dict.value\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务名称\" prop=\"taskName\">\n              <el-input v-model=\"form.taskName\" placeholder=\"请输入任务名称\" maxlength=\"20\" show-word-limit/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"任务内容\" prop=\"content\">\n              <el-input type=\"textarea\" v-model=\"form.content\" placeholder=\"请输入任务内容\" maxlength=\"500\" show-word-limit/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务开始时间\" prop=\"startTime\">\n              <el-date-picker type=\"datetime\" placeholder=\"请选择\" v-model=\"form.startTime\" :picker-options=\"startPickerOptions\" style=\"width: 100%;\" value-format=\"yyyy-MM-dd HH:mm:ss\" :default-value=\"defaultStartTime\"></el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务完成时间\" prop=\"endTime\">\n              <el-date-picker type=\"datetime\" placeholder=\"请选择\" v-model=\"form.endTime\" :picker-options=\"endPickerOptions\" style=\"width: 100%;\" value-format=\"yyyy-MM-dd HH:mm:ss\" :default-value=\"defaultStartTime\"></el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"责任人\" prop=\"manageUser\">\n              <user-select v-model=\"form.manageUser\"></user-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"关联事务\" prop=\"operateWorkId\">\n              <el-select v-model=\"form.operateWorkId\" clearable filterable placeholder=\"请选择\">\n                <el-option v-for=\"item in operateWorkList\"\n                           :key=\"item.id\" :label=\"item.workName\"\n                           :value=\"item.id\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\" :disabled=\"btnLoading\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listWorkHwTask, getWorkHwTask, delWorkHwTask, addWorkHwTask, updateWorkHwTask,getStageTree } from \"@/api/aqsoc/work-hw/workHwTask\";\nimport {listOperateWork} from \"@/api/operateWork/operateWork\"\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nexport default {\n  name: \"Plan\",\n  components: {UserSelect},\n  dicts: ['hw_stage_class'],\n  props:{\n    workInfo: {\n      type: Object,\n      required: true,\n      default: {}\n    },\n    show: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      dataList: null,\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      // 表单校验\n      rules: {\n        taskName: [\n          { required: true, message: \"任务名称不能为空\", trigger: \"blur\" },\n          { max: 20, message: \"长度不能超过20个字符\", trigger: \"blur\" }\n        ],\n        content: [\n          { required: true, message: \"任务内容不能为空\", trigger: \"blur\" },\n          { max: 500, message: \"长度不能超过500个字符\", trigger: \"blur\" }\n        ],\n        startTime: [\n          { required: true, message: \"任务开始时间不能为空\", trigger: \"blur\" }\n        ],\n        endTime: [\n          { required: true, message: \"任务完成时间不能为空\", trigger: \"blur\" }\n        ],\n        manageUser: [\n          { required: true, message: \"责任人不能为空\", trigger: \"blur\" }\n        ],\n        operateWorkId: [\n          { required: true, message: \"关联事务不能为空\", trigger: \"blur\" }\n        ],\n        stageClass: [\n          { required: true, message: \"HW阶段不能为空\", trigger: \"blur\" }\n        ]\n      },\n      form: {},\n      stageList: [],\n      operateWorkList: [],\n      title: '',\n      btnLoading: false,\n      startPickerOptions: {\n        disabledDate: (time) => {\n          return new Date(time).getTime() < new Date(this.workInfo.hwStart).getTime() || new Date(time).getTime() > new Date(this.form.endTime?this.form.endTime : this.workInfo.hwEnd).getTime();\n        },\n      },\n      endPickerOptions: {\n        disabledDate: (time) => {\n          return new Date(time).getTime() < new Date(this.form.startTime?this.form.startTime : this.workInfo.hwStart).getTime() || new Date(time).getTime() > new Date(this.workInfo.hwEnd).getTime();\n        },\n      },\n      defaultStartTime: new Date(this.workInfo.hwStart),\n    };\n  },\n  watch: {\n  },\n  created() {\n    this.queryParams.workId = this.workInfo.id;\n    this.getStageList();\n    this.getList();\n    this.getOperateWorkList();\n  },\n  methods: {\n    search() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 查询HW事务任务列表 */\n    getList() {\n      this.loading = true;\n      listWorkHwTask(this.queryParams).then(response => {\n        this.dataList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    getStageList(){\n      getStageTree({workId: this.workInfo.id}).then(res => {\n        let arr = res.data;\n        let first = {\n          value: null,\n          label: '全部阶段',\n          sort: 0,\n          count: 0\n        };\n        if(arr && arr.length>0){\n          arr.forEach(item => {\n            first.count += item.count;\n          })\n        }\n        arr.unshift(first);\n        this.stageList = arr;\n      })\n    },\n    getOperateWorkList(){\n      listOperateWork({\n        queryAllData: true,\n        workType: 2\n      }).then(res => {\n        this.operateWorkList = res.rows;\n      })\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      let match = this.operateWorkList.find(item => item.workName === 'HW专项');\n      if(match != null){\n        this.form.operateWorkId = match.id;\n      }\n      this.open = true;\n      this.title = \"添加HW事务任务\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getWorkHwTask(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改HW事务任务\";\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除HW事务任务编号为\"' + ids + '\"的数据项？').then(function () {\n        return delWorkHwTask(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {\n      });\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        workId: this.workInfo.id\n      };\n      this.resetForm(\"form\");\n    },\n    back(){\n      this.$emit('update:show',false);\n    },\n    submitForm(){\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          this.btnLoading = true;\n          this.form.workId = this.workInfo.id;\n          let currentForm = {...this.form};\n          if (this.form.id != null) {\n            updateWorkHwTask(currentForm).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            }).finally(() => {\n              this.btnLoading = false;\n            });\n          } else {\n            addWorkHwTask(currentForm).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            }).finally(() => {\n              this.btnLoading = false;\n            });\n          }\n        }\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    tabClick(tab){\n      if(tab.paneName !== '0'){\n        this.queryParams.stageClass = tab.paneName;\n      }else {\n        this.queryParams.stageClass = null;\n      }\n      this.search();\n    },\n    getPickerOptions(){\n      let startTime = this.workInfo.hwStart;\n      let endTime = this.workInfo.hwEnd;\n      return {\n        disabledDate: (time) => {\n          console.log(time)\n          return new Date(time).getTime() < new Date(startTime).getTime() || new Date(time).getTime() > new Date(endTime).getTime();\n        },\n      }\n    },\n  }\n};\n</script>\n<style lang=\"scss\" scoped>\n.main{\n  width: 100%;\n\n  .head-box{\n    background: #fff;\n    flex-shrink: 0;\n    margin-bottom: 10px;\n    padding: 15px 10px 15px;\n    position: relative;\n    display: flex;\n\n    .left{\n      align-content: center;\n      font-size: 16px;\n      font-weight: 700;\n    }\n    .right{\n      flex: 1;\n      text-align: right;\n    }\n\n    .btn2{\n      height: 32px;\n      color: #656C75;\n      font-size: 14px;\n      border: 1px solid #dbdbdb;\n      background: #f2f7f7;\n    }\n  }\n\n  .work-tabs{\n    ::v-deep .el-tabs__item {\n      height: 48px !important;\n      line-height: 48px !important;\n      text-align: left !important;\n      width: 285px !important;\n      padding: 0 10px;\n    }\n    /*设置第一个标签项不使用通用的悬停和选中效果*/\n    ::v-deep .el-tabs__item:not(#tab-search):hover {\n      color: #333 !important;\n      background-color: #f5f5f5 !important;\n    }\n    ::v-deep .el-tabs__item:not(#tab-search).is-active {\n      line-height: 48px;\n      color: #333333;\n      font-weight: bold;\n      background-color: #f5f5f5;\n    }\n    .work-tabs-label {\n      padding: 0 10px;\n    }\n  }\n}\n</style>\n"]}]}