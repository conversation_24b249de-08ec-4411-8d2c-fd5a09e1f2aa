package com.ruoyi.ffsafe.api.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Threads;
import com.ruoyi.ffsafe.api.domain.*;
import com.ruoyi.ffsafe.api.service.IFfsafeInterfaceConfigService;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 主机入侵攻击事件拉取事件
 */
@Slf4j
@Component
public class PullHostIntrusionAttackEvent extends BaseEvent {
    private boolean bRun;
    private boolean bFirst = true;
    private FfsafeInterfaceConfig interfaceConfig;

    @Autowired
    private FfsafeClientService ffsafeClientService;

    @Autowired
    private IFfsafeInterfaceConfigService ffsafeInterfaceConfigService;
    @Resource
    private ITblDeviceConfigService deviceConfigService;

    /**
     * 获取主机入侵攻击参数
     */
    private HostIntrusionAttackParam getHostIntrusionAttackParam(FfsafeInterfaceConfig ffsafeInterfaceConfig) {
        HostIntrusionAttackParam param = new HostIntrusionAttackParam();
        Date lastDataTime = ffsafeInterfaceConfig.getDataLastTime();
        if (lastDataTime == null) {
            // 默认拉取30天前的数据
            lastDataTime = DateUtils.addDays(DateUtils.getNowDate(), -30);
        }

        // 设置开始时间为上次同步时间，往前回退1天防止数据遗漏
        Date startTime = DateUtils.addDays(lastDataTime, -1);
        param.setStartDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, startTime));

        // 设置结束时间为当前时间前2分钟，避免获取正在生成的数据
        Date endTime = DateUtils.addMinutes(DateUtils.getNowDate(), -2);
        param.setEndDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime));

        return param;
    }

    @PostConstruct
    public void init() {
        log.info("开始获取主机入侵攻击数据线程...");
        bRun = true;
        startEvent();
    }

    protected void startEvent() {
        Thread event = new Thread(new Runnable() {
            @Override
            public void run() {
                while (bRun){
                    TblDeviceConfig queryDeviceConfig = new TblDeviceConfig();
                    queryDeviceConfig.setStatus(1);
                    List<TblDeviceConfig> list = deviceConfigService.selectTblDeviceConfigList(queryDeviceConfig);
                    if(CollUtil.isNotEmpty(list)){
                        List<Runnable> tasks = new ArrayList<>();
                        list.forEach(deviceConfig -> {
                            tasks.add(() -> {
                                try {
                                    log.info("开始获取主机入侵攻击数据: {}",deviceConfig.getDeviceName());
                                    // 获取参数并调用接口
                                    FfsafeApiConfig ffsafeApiConfig = deviceConfigService.getFfsafeApiConfig(deviceConfig);
                                    if(!ffsafeApiConfig.isEnable()){
                                        log.info("ffsafe未启用: {}",deviceConfig);
                                        throw new ServiceException("ffsafe未启用");
                                    }

                                    FfsafeInterfaceConfig ffsafeInterfaceConfig = new FfsafeInterfaceConfig();
                                    ffsafeInterfaceConfig.setDataLastTime(deviceConfig.getHostIntrusionLastTime());
                                    FfsafeClientService.deviceConfigThreadLocal.set(deviceConfig);
                                    HostIntrusionAttackParam param = getHostIntrusionAttackParam(ffsafeInterfaceConfig);
                                    param.setDeviceConfigId(deviceConfig.getId());
                                    boolean bRet = ffsafeClientService.pullHostIntrusionAttacks(param, ffsafeApiConfig);
                                    if (bRet) {
                                        //更新最后更新日期
                                        TblDeviceConfig update = new TblDeviceConfig();
                                        update.setId(deviceConfig.getId());
                                        update.setHostIntrusionLastTime(DateUtil.parse(param.getEndDate()));
                                        deviceConfigService.updateLastTime(update);
                                    }
                                } catch (Exception e) {
                                    log.error("主机入侵攻击数据同步失败: {},config: {}",e.getMessage(),deviceConfig);
                                } finally {
                                    FfsafeClientService.deviceConfigThreadLocal.remove();
                                }
                            });
                        });
                        Threads.batchAsyncExecute(tasks);
                    }
                    ThreadUtil.sleep(10000);
                }
            }
        });
        event.start();
    }
}
