package com.ruoyi.ffsafe.api.service.impl;

import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttackDetail;
import com.ruoyi.ffsafe.api.mapper.FfsafeHostIntrusionAttackDetailMapper;
import com.ruoyi.ffsafe.api.service.IFfsafeHostIntrusionAttackDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 主机入侵攻击详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
public class FfsafeHostIntrusionAttackDetailServiceImpl implements IFfsafeHostIntrusionAttackDetailService {
    
    @Autowired
    private FfsafeHostIntrusionAttackDetailMapper ffsafeHostIntrusionAttackDetailMapper;

    /**
     * 查询主机入侵攻击详情
     * 
     * @param id 主机入侵攻击详情主键
     * @return 主机入侵攻击详情
     */
    @Override
    public FfsafeHostIntrusionAttackDetail selectFfsafeHostIntrusionAttackDetailById(Long id) {
        return ffsafeHostIntrusionAttackDetailMapper.selectFfsafeHostIntrusionAttackDetailById(id);
    }

    /**
     * 查询主机入侵攻击详情列表
     * 
     * @param ffsafeHostIntrusionAttackDetail 主机入侵攻击详情
     * @return 主机入侵攻击详情
     */
    @Override
    public List<FfsafeHostIntrusionAttackDetail> selectFfsafeHostIntrusionAttackDetailList(FfsafeHostIntrusionAttackDetail ffsafeHostIntrusionAttackDetail) {
        return ffsafeHostIntrusionAttackDetailMapper.selectFfsafeHostIntrusionAttackDetailList(ffsafeHostIntrusionAttackDetail);
    }

    /**
     * 根据攻击事件ID查询详情列表
     *
     * @param attackId 攻击事件ID
     * @return 详情列表
     */
    @Override
    public List<FfsafeHostIntrusionAttackDetail> selectByAttackId(Long attackId) {
        return ffsafeHostIntrusionAttackDetailMapper.selectByAttackId(attackId);
    }

    /**
     * 根据攻击事件ID列表批量查询详情列表
     *
     * @param attackIds 攻击事件ID列表
     * @return 详情列表
     */
    @Override
    public List<FfsafeHostIntrusionAttackDetail> selectByAttackIds(List<Long> attackIds) {
        return ffsafeHostIntrusionAttackDetailMapper.selectByAttackIds(attackIds);
    }

    /**
     * 新增主机入侵攻击详情
     * 
     * @param ffsafeHostIntrusionAttackDetail 主机入侵攻击详情
     * @return 结果
     */
    @Override
    public int insertFfsafeHostIntrusionAttackDetail(FfsafeHostIntrusionAttackDetail ffsafeHostIntrusionAttackDetail) {
        return ffsafeHostIntrusionAttackDetailMapper.insertFfsafeHostIntrusionAttackDetail(ffsafeHostIntrusionAttackDetail);
    }

    /**
     * 修改主机入侵攻击详情
     * 
     * @param ffsafeHostIntrusionAttackDetail 主机入侵攻击详情
     * @return 结果
     */
    @Override
    public int updateFfsafeHostIntrusionAttackDetail(FfsafeHostIntrusionAttackDetail ffsafeHostIntrusionAttackDetail) {
        return ffsafeHostIntrusionAttackDetailMapper.updateFfsafeHostIntrusionAttackDetail(ffsafeHostIntrusionAttackDetail);
    }

    /**
     * 批量删除主机入侵攻击详情
     * 
     * @param ids 需要删除的主机入侵攻击详情主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeHostIntrusionAttackDetailByIds(Long[] ids) {
        return ffsafeHostIntrusionAttackDetailMapper.deleteFfsafeHostIntrusionAttackDetailByIds(ids);
    }

    /**
     * 删除主机入侵攻击详情信息
     * 
     * @param id 主机入侵攻击详情主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeHostIntrusionAttackDetailById(Long id) {
        return ffsafeHostIntrusionAttackDetailMapper.deleteFfsafeHostIntrusionAttackDetailById(id);
    }

    /**
     * 根据攻击事件ID删除详情
     * 
     * @param attackId 攻击事件ID
     * @return 结果
     */
    @Override
    public int deleteFfsafeHostIntrusionAttackDetailByAttackId(Long attackId) {
        return ffsafeHostIntrusionAttackDetailMapper.deleteFfsafeHostIntrusionAttackDetailByAttackId(attackId);
    }

    /**
     * 批量插入主机入侵攻击详情
     * 
     * @param list 主机入侵攻击详情列表
     * @return 结果
     */
    @Override
    public int batchInsertFfsafeHostIntrusionAttackDetail(List<FfsafeHostIntrusionAttackDetail> list) {
        return ffsafeHostIntrusionAttackDetailMapper.batchInsertFfsafeHostIntrusionAttackDetail(list);
    }

    /**
     * 批量根据攻击事件ID删除详情
     * 
     * @param attackIds 攻击事件ID列表
     * @return 结果
     */
    @Override
    public int batchDeleteByAttackIds(List<Long> attackIds) {
        return ffsafeHostIntrusionAttackDetailMapper.batchDeleteByAttackIds(attackIds);
    }
}
