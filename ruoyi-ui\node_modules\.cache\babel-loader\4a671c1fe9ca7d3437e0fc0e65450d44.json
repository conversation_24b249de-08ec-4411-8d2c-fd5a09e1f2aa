{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\mixins\\generator\\timerMixin.js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\mixins\\generator\\timerMixin.js", "mtime": 1756294198439}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1751956515056}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8qKgogKiDlrprml7blmajmt7flhaXvvIzlrprml7bosIPnlKjorr7lpIfmjojmnYPnirbmgIEKICovCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGltZXI6IG51bGwKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBzdGFydFRpbWVyOiBmdW5jdGlvbiBzdGFydFRpbWVyKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLnRpbWVyID0gc2V0SW50ZXJ2YWwoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLiRzdG9yZS5kaXNwYXRjaCgnZ2V0QXV0aFN0YXR1cycpLnRoZW4oZnVuY3Rpb24gKHJlcykge30pOwogICAgICB9LCAxMDAwICogNjAgKiAxNSk7IC8vIOavjzE1bWlu6LCD55So5LiA5qyhCiAgICB9LAogICAgc3RvcFRpbWVyOiBmdW5jdGlvbiBzdG9wVGltZXIoKSB7CiAgICAgIGlmICh0aGlzLnRpbWVyKSB7CiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnRpbWVyKTsKICAgICAgICB0aGlzLnRpbWVyID0gbnVsbDsKICAgICAgfQogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuc3RhcnRUaW1lcigpOyAvLyDnu4Tku7bliJvlu7rml7blkK/liqjlrprml7blmagKICB9LAogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICB0aGlzLnN0b3BUaW1lcigpOyAvLyDnu4Tku7bplIDmr4HliY3lgZzmraLlrprml7blmagKICB9Cn07"}, {"version": 3, "names": ["_default", "exports", "default", "data", "timer", "methods", "startTimer", "_this", "setInterval", "$store", "dispatch", "then", "res", "stopTimer", "clearInterval", "created", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["E:/wsh/augment_workspace/aqsoc-main/ruoyi-ui/src/mixins/generator/timerMixin.js"], "sourcesContent": ["/**\n * 定时器混入，定时调用设备授权状态\n */\n\nexport default {\n  data() {\n    return {\n      timer: null,\n    };\n  },\n  methods: {\n    startTimer() {\n      this.timer = setInterval(() => {\n        this.$store.dispatch('getAuthStatus').then(res => {})\n      }, 1000 * 60 * 15); // 每15min调用一次\n    },\n    stopTimer() {\n      if (this.timer) {\n        clearInterval(this.timer);\n        this.timer = null;\n      }\n    }\n  },\n  created() {\n    this.startTimer(); // 组件创建时启动定时器\n  },\n  beforeDestroy() {\n    this.stopTimer(); // 组件销毁前停止定时器\n  }\n};\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AAFA,IAAAA,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAIe;EACbC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACPC,UAAU,WAAVA,UAAUA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACX,IAAI,CAACH,KAAK,GAAGI,WAAW,CAAC,YAAM;QAC7BD,KAAI,CAACE,MAAM,CAACC,QAAQ,CAAC,eAAe,CAAC,CAACC,IAAI,CAAC,UAAAC,GAAG,EAAI,CAAC,CAAC,CAAC;MACvD,CAAC,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC;IACDC,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAACT,KAAK,EAAE;QACdU,aAAa,CAAC,IAAI,CAACV,KAAK,CAAC;QACzB,IAAI,CAACA,KAAK,GAAG,IAAI;MACnB;IACF;EACF,CAAC;EACDW,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACT,UAAU,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EACDU,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;EACpB;AACF,CAAC", "ignoreList": []}]}