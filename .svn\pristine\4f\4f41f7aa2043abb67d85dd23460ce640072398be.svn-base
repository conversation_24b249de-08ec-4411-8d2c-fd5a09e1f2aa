package com.ruoyi.threaten.domain;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.utils.poi.ExcelHandlerAdapter;
import com.ruoyi.safe.domain.TblBusinessApplication;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 攻击者视角告警列对象 tbl_attack_alarm
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TblAttackAlarm extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 攻击者IP */
    @Excel(name = "攻击者IP")
    private String attackIp;

    /** 风险等级 */
    @Excel(name = "风险等级",dictType = "alarm_attack_risk_level")
    private Long riskLevel;

    /** 地理位置 */
    @Excel(name = "地理位置")
    private String location;

    /** 攻击目标IP数 */
    @Excel(name = "攻击目标IP数")
    private Long victimIpNums;

    /** 命中规则数 */
    @Excel(name = "命中规则数")
    private Long attackTypeNums;

    /** 告警数量 */
    @Excel(name = "告警数量")
    private Long attackNums;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最早告警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最近告警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Date startUpdateTime;
    private Date endUpdateTime;

    private Long handleState;

    private Date handleTime;

    private String handleDesc;

    private Long handleUser;

    private List<Long> ids;

    /** 标签 */
    private List<TblAttackAlarmTags> tags;

    @Excel(name = "攻击者标签")
    private String tagsStr;

    private Boolean isBlocking = false; //是否阻断中

    private List<TblBusinessApplication> businessApplications;

    /** 阻断状态 1=正在阻断 2=曾经阻断*/
    private Integer blockStatus;

    /** 同步状态 */
    private String synchronizationStatus;

    /** 数据ID */
    private String dataId;

    public void setTags(List<TblAttackAlarmTags> tags) {
        this.tags = tags;
        if(CollUtil.isNotEmpty(tags)){
            this.tagsStr = CollUtil.join(tags.stream().map(TblAttackAlarmTags::getTagName).filter(Objects::nonNull).collect(Collectors.toList()),  ",");
        }
    }
}
