<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          ref="form"
          :model="form"
          size="small"
          :inline="true"
          label-position="right"
          label-width="70px">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="综合风险">
                <el-select v-model="form.attackType" placeholder="请选择综合风险">
                  <el-option label="高中" value="0"></el-option>
                  <el-option label="中" value="1"></el-option>
                  <el-option label="低" value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="处置状态">
                <el-select v-model="form.handleState" placeholder="请选择处置状态">
                  <el-option label="待处理" value="0"></el-option>
                  <el-option label="处理中" value="1"></el-option>
                  <el-option label="处理完成" value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="攻击源IP">
                <el-input v-model="form.attackIp" placeholder="请输入攻击源IP"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleQuery"
                >查询</el-button>
                <el-button class="btn2" size="small" @click="resetQuery"
                >重置</el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-down" @click="showAll=true" v-if="!showAll">
                  展开
                </el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-up" @click="showAll=false" v-else>收起
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-if="showAll">
            <el-col :span="6">
              <el-form-item label="文件名">
                <el-input v-model="form.targetIp" placeholder="请输入文件名"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="rangeTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">主机事件列表</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleDetail"
                >批量删除</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleExport"
                >导出
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="tableContainer">
          <el-table
            :data="list"
            height="100%"
            v-loading="loading"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="主机IP" align="left" width="180">
              <template slot-scope="scope">
                <span class="table-serial-number">{{ scope.row.attackIp }}</span>
              </template>
            </el-table-column>
            <el-table-column label="检测事项" align="left" prop="targetIp"/>
            <el-table-column label="综合风险" align="left" prop="threatenName"/>
            <el-table-column label="类别" align="left" prop="threatenTime"/>
            <el-table-column label="处置状态" align="left" prop="threatenLevel"/>
            <el-table-column label="告警时间" align="left" prop="threatenEndTime"/>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click="handleDetail(scope.row)"
                >详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "hostEvent",
  data() {
    return {
      showAll: false,
      rangeTime: [],
      form: {},
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      loading: false,
      multipleSelection: [],
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getList()
    },
    getList() {

    },
    handleQuery() {

    },
    handleDetail(row) {

    },
    handleExport() {

    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    resetQuery() {
      this.form = {
        attackIp: null,
        targetIp: null,
        attackType: null,
        timeRange: null,
        handleState: null
      }
      this.handleQuery()
    },
  }
}
</script>

<style scoped lang="scss">

</style>
