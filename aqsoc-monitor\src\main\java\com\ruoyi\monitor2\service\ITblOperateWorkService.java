package com.ruoyi.monitor2.service;

import java.util.List;

import com.ruoyi.monitor2.domain.OperateWorkCreateInfo;
import com.ruoyi.monitor2.domain.TblOperateWork;

/**
 * 事务管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface ITblOperateWorkService
{
    /**
     * 查询事务管理
     *
     * @param id 事务管理主键
     * @return 事务管理
     */
    public TblOperateWork selectTblOperateWorkById(Long id);

    /**
     * 批量查询事务管理
     *
     * @param ids 事务管理主键集合
     * @return 事务管理集合
     */
    public List<TblOperateWork> selectTblOperateWorkByIds(Long[] ids);

    /**
     * 查询事务管理列表
     *
     * @param tblOperateWork 事务管理
     * @return 事务管理集合
     */
    public List<TblOperateWork> selectTblOperateWorkList(TblOperateWork tblOperateWork);

    /**
     * 新增事务管理
     *
     * @param tblOperateWork 事务管理
     * @return 结果
     */
    public int insertTblOperateWork(TblOperateWork tblOperateWork);

    /**
     * 修改事务管理
     *
     * @param tblOperateWork 事务管理
     * @return 结果
     */
    public int updateTblOperateWork(TblOperateWork tblOperateWork);

    /**
     * 删除事务管理信息
     *
     * @param id 事务管理主键
     * @return 结果
     */
    public int deleteTblOperateWorkById(Long id);

    /**
     * 批量删除事务管理
     *
     * @param ids 需要删除的事务管理主键集合
     * @return 结果
     */
    public int deleteTblOperateWorkByIds(Long[] ids);

    List<TblOperateWork> selectTblOperateWorkByName(TblOperateWork operateWork);

    /**
     * 处理cron任务
     * @param id
     */
    void handleCronTask(Long id);

    void handleCronTask(List<Long> ids);

    List<OperateWorkCreateInfo> getOperateWorkCreateList(TblOperateWork operateWork);

    String createWork(TblOperateWork operateWork);
    String createFlowTask(TblOperateWork operateWork);

    String createWorkFlowTask(TblOperateWork operateWork);

    int count(TblOperateWork operateWork);
}
