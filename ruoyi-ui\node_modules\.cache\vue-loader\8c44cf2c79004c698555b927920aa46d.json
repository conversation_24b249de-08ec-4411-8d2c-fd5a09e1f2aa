{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\applicationDetails.vue?vue&type=style&index=0&id=2fe6abd8&scoped=true&lang=scss", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\applicationDetails.vue", "mtime": 1756294198406}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouY2hlY2tfZm9yIHsKICB6LWluZGV4OiA5OwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKfQoKLmRvd24gewogIHdpZHRoOiAxMDAlOwogIG1hcmdpbi10b3A6IDEwcHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwp9Cgo6OnYtZGVlcCAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW0uaXMtYWN0aXZlIHsKICBiYWNrZ3JvdW5kOiByZ2JhKDQ4LCAxMTEsIDIyOSwgMC44KTsKICBjb2xvcjogI0ZGRkZGRjsKfQoKLm1fbWFyayB7CiAgd2lkdGg6IDEwMCU7CiAgYm9yZGVyOiBzb2xpZCAxcHggcmVkOwogIGJvcmRlci1yYWRpdXM6IDEwcHg7CiAgaGVpZ2h0OiA4MHB4OwogIHBhZGRpbmc6IDVweDsKICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9Cgo6OnYtZGVlcCAuZWwtdGFicyB7CiAgaGVpZ2h0OiAxMDAlOwoKICAuZWwtdGFic19fY29udGVudCB7CiAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDU2cHgpOwoKICAgIC5lbC10YWItcGFuZSB7CiAgICAgIGhlaWdodDogMTAwJTsKICAgIH0KICB9Cn0KCjo6di1kZWVwIC5lbC1kaWFsb2dfX2JvZHkgewogIHBhZGRpbmc6IDAgMCAxMHB4IDIwcHg7Cn0KCi5kaWFsb2ctYm9keSB7CiAgaGVpZ2h0OiA3MDBweDsKICBvdmVyZmxvdzogYXV0bzsKfQoK"}, {"version": 3, "sources": ["applicationDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkSA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "applicationDetails.vue", "sourceRoot": "src/views/hhlCode/component", "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"title\"\n    :visible.sync=\"dialogVisible\"\n    width=\"80%\"\n    @open=\"openDialog\"\n    @close=\"closeDialog\"\n    :before-close=\"handleClose\">\n    <div class=\"dialog-body\">\n      <div\n        v-if=\"application !== null && application.remark !== '' && application.remark !== undefined && application.checkOn === 'wait'\"\n        class=\"m_mark\">\n        <p style=\"color: red\">{{ application.remark }}</p>\n      </div>\n\n      <div style=\"float:right; margin-bottom: 15px\" v-hasPermi=\"['safe:application:check']\"\n           v-if=\"this.whetherOrNotToAudit\">\n        <el-row :gutter=\"20\" class=\"check_for\" v-if=\" gv('checkOn',application,null)=='wait'\">\n          <el-col :span=\"1.5\">\n            <el-button type=\"primary\" @click=\"checkPass()\">审核通过</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button @click=\"open = true;\">审核不通过</el-button>\n          </el-col>\n        </el-row>\n      </div>\n      <div :style=\"tabName === 'firewallNat' ? { flex: 1, overflow: 'auto' } : { flex: 1 }\" v-if=\"dialogVisible\">\n        <el-tabs v-model=\"tabName\" type=\"card\" :before-leave=\"changeTab\" v-if=\"title.includes('查看')\">\n          <el-tab-pane label=\"系统详情\" name=\"base\"/>\n          <el-tab-pane label=\"业务系统漏洞\" name=\"webBusinessGap\" v-if=\"isShowGap\"/>\n          <el-tab-pane label=\"端口暴露面\" name=\"firewallNat\" v-if=\"isShowGap\"/>\n        </el-tabs>\n        <SystemDetails\n          v-show=\"!title.includes('查看')\"\n          v-if=\"tabName === 'base'\"\n          ref=\"base\"\n          :asset-list=\"assetList\"\n          :asset-id=\"assetId\"\n          :changeId=\"changeId\"/>\n        <OperationSystemDetails\n          v-show=\"title.includes('查看') && tabName  === 'base'\"\n          :asset-list=\"assetList\"\n          :asset-id=\"assetId\"\n          :changeId=\"changeId\"/>\n        <business-gap v-if=\"tabName === 'webBusinessGap' && isShowGap\" :asset-id=\"assetId\"/>\n        <firewall-nat-index\n          v-if=\"tabName === 'firewallNat'\"\n          style=\"height: calc(100% - 10px); display: flex; flex-direction: column\"\n          :current-application-id=\"assetId\"\n          :hidden-columns=\"['serverList','businessApplicationList','tools']\"/>\n      </div>\n\n      <el-dialog title=\"业务应用审核\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n        <el-form ref=\"form\" label-width=\"130px\">\n          <div style=\"font-size: 22px;\">不通过审核意见</div>\n          <br>\n          <div>\n            <el-input v-model=\"content\" :autosize=\"{minRows: 4, maxRows: 6}\" type=\"textarea\" placeholder=\"请输入意见\"/>\n          </div>\n        </el-form>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"checkFail()\">确定</el-button>\n          <el-button @click=\"open=false;\">取 消</el-button>\n        </div>\n      </el-dialog>\n\n    </div>\n    <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button\n          type=\"primary\"\n          @click=\"handleAfter\"\n          v-if=\"!title.includes('查看')\">\n          保存\n        </el-button>\n<!--        <el-button\n          type=\"primary\"\n          @click=\"handleSave\"\n          v-if=\"gv('checkOn',application)!='wait' && showData.value && ['component'].includes(tabName)\">\n          保存\n        </el-button>\n        <el-button\n          v-if=\"['business','base'].includes(tabName) && !isShowGap\" type=\"primary\" @click=\"handleAfter\"\n                   :loading=\"afterBtnLoad\">下一步\n        </el-button>-->\n    <el-button @click=\"handleClose\">关 闭</el-button>\n    </span>\n  </el-dialog>\n</template>\n\n<script>\nimport {getValFromObject} from \"@/utils\";\nimport {getApplication, checkApplication,auditConfig} from \"@/api/safe/application\";\nimport assetRegister from \"@/mixins/assetRegister\";\n\nexport default {\n  name: \"applicationInfo\",\n  mixins: [assetRegister],\n  components: {\n    SystemDetails: () => import('@/views/hhlCode/component/systemDetails'),\n    FirewallNatIndex: () => import('@/views/safe/safety/firewallNatIndex'),\n    BusinessGap: () => import('../businessGap'),\n    ApplicationForm: () => import('@/views/hhlCode/component/application/applicationForm'),\n    ApplicationHardware: () => import('@/views/hhlCode/component/application/applicationHardware'),\n    businessApplicationStatus: () => import('@/views/hhlCode/component/businessApplicationStatus'),\n    OperationSystemDetails: () => import('@/views/hhlCode/component/OperationSystemDetails'),\n  },\n  props: {\n    applicationVisible: {\n      type: Boolean,\n      required: true\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    params: {\n      type: Object,\n      default: () => {},\n    },\n  },\n  provide() {\n    return {\n      $editable: this.showData\n    }\n  },\n  data() {\n    return {\n      tabName: 'base',\n      showData: {value: true},\n      editable: false,\n      assetId: null,\n      application: null,\n      open: false,\n      content: null,\n      ref: ['base', 'business', 'component'],//提交组件\n      gv: getValFromObject,\n      rules: {},\n      whetherOrNotToAudit: false,\n      isShowGap: true,\n      afterBtnLoad: false,\n      isTempSave: false,\n      assetAllocationType: '1',\n    }\n  },\n  computed: {\n    dialogVisible: {\n      get() {\n        return this.applicationVisible\n      },\n      set(value) {\n        this.$emit('update:applicationVisible', value)\n      }\n    }\n  },\n  created() {\n    auditConfig({\n      pageNum: 1,\n      pageSize: 10\n    }).then(response => {\n      this.whetherOrNotToAudit = response.rows[0].configValue !== 'false';\n    })\n  },\n  mounted() {},\n\n  methods: {\n    changeId(id) {\n      this.assetId = id;\n    },\n    // 保存\n    // async handleSave() {\n    //   await this.$refs[this.tabName].handleSave().then(()=>{\n    //     this.$emit('applicationChange', true);\n    //   });\n    // },\n\n    // 下一步（保存）\n    async handleAfter() {\n      // if (this.isShowGap) {\n      //   if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1];\n      //   return true;\n      // }\n      if (!this.assetId) {\n        // 新增时，检验基本信息必填项\n        if (!this.$refs['base'].validateForm()) return this.$modal.msgError(\"请先录入基本信息!\");\n      } else {\n        // 编辑时，检验基本信息必填项\n        if (this.tabName === 'base') if (!this.$refs['base'].validateForm()) return this.$modal.msgError(\"请先录入基本信息!\");\n      }\n      this.afterBtnLoad = true;\n      await this.$refs[this.tabName].handleSave().then(()=>{\n        this.$emit('applicationChange', false);\n      }).finally(() => {\n        this.afterBtnLoad = false;\n        this.$emit('deptSelectKeyChange', true);\n      });\n      // if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1]\n    },\n    async handleAfter2() {\n      // if (this.isShowGap) {\n      //   if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1];\n      //   return true;\n      // }\n      if (!this.assetId) {\n        // 新增时，检验基本信息必填项\n        if (!this.$refs['base'].validateForm()) return this.$modal.msgError(\"请先录入基本信息!\");\n      } else {\n        // 编辑时，检验基本信息必填项\n        if (this.tabName === 'base') if (!this.$refs['base'].validateForm()) return this.$modal.msgError(\"请先录入基本信息!\");\n      }\n      this.afterBtnLoad = true;\n      await this.$refs[this.tabName].handleSave().then(()=>{\n        this.isTempSave = true;\n      }).finally(() => {\n        this.afterBtnLoad = false;\n      });\n      // if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1]\n    },\n    changeTab(newName, oldName) {\n      if (this.assetId == null && newName !== 'base') {\n        this.$modal.msgError(\"请先录入系统基本信息，保存后再切换!\");\n        this.tabName = 'base';\n        return false;\n      }\n      let components = ['base', 'business', 'component'];\n      this.step = components.findIndex(item => item === newName)\n    },\n    checkPass() {\n      let data = {assetId: this.assetId, operator: \"pass\"}\n      this.$modal.confirm('是否确认要审核通过该项填报？').then(function () {\n        checkApplication(data);\n      }).then(() => {\n        this.application.checkOn = 'pass';\n        this.$modal.msgSuccess('已经完成审核！');\n      });\n    },\n    checkFail() {\n      if (this.content != null && this.content !== '') {\n        if (this.content.length > 255) {\n          this.$message.error(\"字符大小超过255！\");\n          return;\n        }\n        let data = {assetId: this.assetId, operator: \"fail\", content: this.content}\n        checkApplication(data).then(res => {\n          this.application.checkOn = 'fail';\n          this.open = false;\n          this.$modal.msgSuccess('已经完成审核！');\n        });\n      } else {\n        this.$message.error(\"请输入不通过审核意见！\");\n\n      }\n    },\n\n    // 打开弹窗\n    openDialog() {\n      // if (this.params && this.params.assetId) {\n      //   this.handleIntoPage();\n      //   return;\n      // }\n      this.isShowGap = this.params.isShowGap !== undefined\n      if (this.params.assetId && !this.assetId) {\n        this.assetId = this.params.assetId;\n        this.showData.value = this.params.showData ? this.params.showData === \"true\" : true;\n        getApplication(this.assetId).then(res => {\n          this.application = res.data.applicationVO;\n        });\n        if (this.params.details && this.params.tabName) {\n          this.tabName = this.params.tabName;\n        }\n      }\n    },\n\n    // 关闭弹窗事件\n    closeDialog() {\n      this.assetId = null;\n      this.tabName = 'base';\n      this.showData.value = true;\n      this.$emit('close')\n    },\n\n    // 关闭弹窗\n    handleClose() {\n      this.showData.value = true;\n      this.dialogVisible = false;\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.check_for {\n  z-index: 9;\n  position: relative;\n}\n\n.down {\n  width: 100%;\n  margin-top: 10px;\n  text-align: center;\n}\n\n::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\n  background: rgba(48, 111, 229, 0.8);\n  color: #FFFFFF;\n}\n\n.m_mark {\n  width: 100%;\n  border: solid 1px red;\n  border-radius: 10px;\n  height: 80px;\n  padding: 5px;\n  margin-bottom: 10px;\n}\n\n::v-deep .el-tabs {\n  height: 100%;\n\n  .el-tabs__content {\n    height: calc(100% - 56px);\n\n    .el-tab-pane {\n      height: 100%;\n    }\n  }\n}\n\n::v-deep .el-dialog__body {\n  padding: 0 0 10px 20px;\n}\n\n.dialog-body {\n  height: 700px;\n  overflow: auto;\n}\n\n</style>\n"]}]}