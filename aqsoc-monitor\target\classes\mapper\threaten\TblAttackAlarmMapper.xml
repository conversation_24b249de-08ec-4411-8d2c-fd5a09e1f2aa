<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.threaten.mapper.TblAttackAlarmMapper">

    <resultMap type="TblAttackAlarm" id="TblAttackAlarmResult">
        <result property="id"    column="id"    />
        <result property="attackIp"    column="attack_ip"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="location"    column="location"    />
        <result property="victimIpNums"    column="victim_ip_nums"    />
        <result property="attackTypeNums"    column="attack_type_nums"    />
        <result property="attackNums"    column="attack_nums"    />
        <result property="startTime"    column="start_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="handleState"    column="handle_state"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleDesc"    column="handle_desc"    />
        <result property="handleUser"    column="handle_user"    />
        <result property="synchronizationStatus"    column="synchronization_status"    />
        <collection property="tags" select="com.ruoyi.threaten.mapper.TblAttackAlarmTagsMapper.selectTblAttackAlarmTagsByAttackId" column="id" />
    </resultMap>

    <sql id="selectTblAttackAlarmVo">
        select distinct t1.id, t1.attack_ip, t1.risk_level, t1.location, t1.victim_ip_nums, t1.attack_type_nums, t1.attack_nums,t1.handle_state, t1.start_time, t1.update_time, t1.synchronization_status from tbl_attack_alarm t1
    </sql>

    <select id="selectTblAttackAlarmList" parameterType="TblAttackAlarm" resultMap="TblAttackAlarmResult">
        <include refid="selectTblAttackAlarmVo"/>
        <if test="blockStatus != null">
            <if test="blockStatus == 1"> INNER JOIN ffsafe_ipfilter_blocking blocking ON blocking.ip = t1.attack_ip</if>
            <if test="blockStatus == 2"> INNER JOIN ffsafe_ipfilter_log blockLog ON blockLog.ip = t1.attack_ip</if>
        </if>
        <where>
            <if test="attackIp != null  and attackIp != ''"> and t1.attack_ip = #{attackIp}</if>
            <if test="riskLevel != null "> and t1.risk_level = #{riskLevel}</if>
            <if test="location != null and location != ''">
                <if test="location == 1"> and t1.location = '局域网'</if>
                <if test="location == 2"> and t1.location != '局域网'</if>
            </if>
            <if test="victimIpNums != null "> and t1.victim_ip_nums = #{victimIpNums}</if>
            <if test="attackTypeNums != null "> and t1.attack_type_nums = #{attackTypeNums}</if>
            <if test="attackNums != null "> and t1.attack_nums = #{attackNums}</if>
            <if test="params != null and params.startTime != null"> and t1.start_time >= #{params.startTime}</if>
            <if test="params != null and params.endTime != null"> and t1.update_time &lt;= #{params.endTime}</if>
            <if test="params != null and params.ips != null and params.ips.size() > 0">
                and t1.attack_ip in
                <foreach item="ip" collection="params.ips" open="(" separator="," close=")">
                    #{ip}
                </foreach>
            </if>
            <if test="startUpdateTime != null"> and t1.update_time >= #{startUpdateTime}</if>
            <if test="endUpdateTime != null"> and t1.update_time &lt;= #{endUpdateTime}</if>
        </where>
        order by t1.update_time desc
    </select>

    <select id="selectTblAttackAlarmById" parameterType="Long" resultMap="TblAttackAlarmResult">
        <include refid="selectTblAttackAlarmVo"/>
        where t1.id = #{id}
    </select>

    <select id="selectTblAttackAlarmByIds" parameterType="Long" resultMap="TblAttackAlarmResult">
        <include refid="selectTblAttackAlarmVo"/>
        where t1.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblAttackAlarm" parameterType="TblAttackAlarm" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_attack_alarm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="attackIp != null">attack_ip,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="location != null">location,</if>
            <if test="victimIpNums != null">victim_ip_nums,</if>
            <if test="attackTypeNums != null">attack_type_nums,</if>
            <if test="attackNums != null">attack_nums,</if>
            <if test="startTime != null">start_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="synchronizationStatus != null">synchronization_status,</if>
            <if test="handleState != null">handle_state,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="handleDesc != null">handle_desc,</if>
            <if test="handleUser != null">handle_user,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="attackIp != null">#{attackIp},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="location != null">#{location},</if>
            <if test="victimIpNums != null">#{victimIpNums},</if>
            <if test="attackTypeNums != null">#{attackTypeNums},</if>
            <if test="attackNums != null">#{attackNums},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="synchronizationStatus != null">#{synchronizationStatus},</if>
            <if test="handleState != null">#{handleState},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="handleDesc != null">#{handleDesc},</if>
            <if test="handleUser != null">#{handleUser},</if>
         </trim>
    </insert>
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tbl_attack_alarm (attack_ip, risk_level,location,victim_ip_nums,attack_type_nums,attack_nums,start_time,update_time,synchronization_status) VALUES
        <foreach collection="list" item="attackAlarm" separator=",">
            (#{attackAlarm.attackIp},#{attackAlarm.riskLevel},#{attackAlarm.location},#{attackAlarm.victimIpNums},#{attackAlarm.attackTypeNums},#{attackAlarm.attackNums},#{attackAlarm.startTime},#{attackAlarm.updateTime},#{attackAlarm.synchronizationStatus})
        </foreach>
    </insert>

    <update id="updateTblAttackAlarm" parameterType="TblAttackAlarm">
        update tbl_attack_alarm
        <trim prefix="SET" suffixOverrides=",">
            <if test="attackIp != null">attack_ip = #{attackIp},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="location != null">location = #{location},</if>
            <if test="victimIpNums != null">victim_ip_nums = #{victimIpNums},</if>
            <if test="attackTypeNums != null">attack_type_nums = #{attackTypeNums},</if>
            <if test="attackNums != null">attack_nums = #{attackNums},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="synchronizationStatus != null">synchronization_status = #{synchronizationStatus},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="handleUser != null">handle_user = #{handleUser},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdate">
        UPDATE tbl_attack_alarm SET
        risk_level = CASE id
        <foreach item="attackAlarm" collection="list" separator=" ">
            WHEN #{attackAlarm.id} THEN <if test="attackAlarm.riskLevel != null">#{attackAlarm.riskLevel}</if> <if test="attackAlarm.riskLevel == null">risk_level</if>
        </foreach>
        ELSE risk_level
        END,
        location = CASE id
        <foreach item="attackAlarm" collection="list" separator=" ">
            WHEN #{attackAlarm.id} THEN <if test="attackAlarm.location != null and attackAlarm.location != ''">#{attackAlarm.location}</if> <if test="attackAlarm.location == null or attackAlarm.location == ''">location</if>
        </foreach>
        ELSE location
        END,
        victim_ip_nums = CASE id
        <foreach item="attackAlarm" collection="list" separator=" ">
            WHEN #{attackAlarm.id} THEN <if test="attackAlarm.victimIpNums != null">#{attackAlarm.victimIpNums}</if> <if test="attackAlarm.victimIpNums == null">victim_ip_nums</if>
        </foreach>
        ELSE victim_ip_nums
        END,
        attack_type_nums = CASE id
        <foreach item="attackAlarm" collection="list" separator=" ">
            WHEN #{attackAlarm.id} THEN <if test="attackAlarm.attackTypeNums != null">#{attackAlarm.attackTypeNums}</if> <if test="attackAlarm.attackTypeNums == null">attack_type_nums</if>
        </foreach>
        ELSE attack_type_nums
        END,
        attack_nums = CASE id
        <foreach item="attackAlarm" collection="list" separator=" ">
            WHEN #{attackAlarm.id} THEN <if test="attackAlarm.attackNums != null">#{attackAlarm.attackNums}</if> <if test="attackAlarm.attackNums == null">attack_nums</if>
        </foreach>
        ELSE attack_nums
        END,
        update_time = CASE id
        <foreach item="attackAlarm" collection="list" separator=" ">
            WHEN #{attackAlarm.id} THEN <if test="attackAlarm.updateTime != null">#{attackAlarm.updateTime}</if> <if test="attackAlarm.updateTime == null">update_time</if>
        </foreach>
        ELSE update_time
        END
    </update>

    <update id="updateBatchTblAttackAlarm">
        update tbl_attack_alarm
        <trim prefix="SET" suffixOverrides=",">
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="handleUser != null">handle_user = #{handleUser},</if>
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteTblAttackAlarmById" parameterType="Long">
        delete from tbl_attack_alarm where id = #{id}
    </delete>

    <delete id="deleteTblAttackAlarmByIds" parameterType="String">
        delete from tbl_attack_alarm where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectNotSyncList" resultType="com.ruoyi.threaten.domain.TblAttackAlarm" resultMap="TblAttackAlarmResult">
        select * from tbl_attack_alarm where (synchronization_status = 0 or synchronization_status is null)
        <if test="startDate != null">
            and update_time &gt;= #{startDate}
        </if>
    </select>
    <select id="groupAlarmLevelStatistics" resultType="com.alibaba.fastjson2.JSONObject">
        select count(1) as alarmNum, risk_level as alarm_level from tbl_attack_alarm ta
        <where>
            <if test="startUpdateTime != null and endUpdateTime != null">
                and ta.update_time BETWEEN #{startUpdateTime} AND #{endUpdateTime}
            </if>
            <if test="riskLevel != null "> and ta.risk_level = #{riskLevel}</if>
        </where>
        group by risk_level
    </select>
</mapper>
