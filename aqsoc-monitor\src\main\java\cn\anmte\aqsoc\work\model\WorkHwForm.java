package cn.anmte.aqsoc.work.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import com.fumixuan.gencode.common.valid.CreateGroup;
import com.fumixuan.gencode.common.valid.UpdateGroup;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <p>
 * 护网事务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Getter
@Setter
@Schema(name = "WorkHwForm", description = "护网事务表")
public class WorkHwForm implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "自增id")
    @NotNull(groups = {UpdateGroup.class})
    private Integer id;


    private Integer templateId;


    @Schema(description = "名称")
    private String name;


    @Schema(description = "年度")
    @NotEmpty(groups = {CreateGroup.class, UpdateGroup.class})
    @Size(max = 64, groups = {CreateGroup.class, UpdateGroup.class})
    private String year;


    @Schema(description = "责任人")
    @NotNull(groups = {CreateGroup.class, UpdateGroup.class})
    private String userId;


    @Schema(description = "HW开始时间")
    @NotNull(groups = {CreateGroup.class, UpdateGroup.class})
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime hwStart;


    @Schema(description = "HW结束时间")
    @NotNull(groups = {CreateGroup.class, UpdateGroup.class})
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime hwEnd;


    @Schema(description = "技术支撑单位")
    private String supportOrgs;


    @Schema(description = "参与人员")
    private String supportUsers;


    @Schema(description = "护网数据json")
    private String dataJson;

    @Schema(description = "所属单位")
    private Long deptId;
}
