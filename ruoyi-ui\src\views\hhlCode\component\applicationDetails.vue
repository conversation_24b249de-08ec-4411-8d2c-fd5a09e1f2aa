<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="80%"
    @open="openDialog"
    @close="closeDialog"
    :before-close="handleClose">
    <div class="dialog-body">
      <div
        v-if="application !== null && application.remark !== '' && application.remark !== undefined && application.checkOn === 'wait'"
        class="m_mark">
        <p style="color: red">{{ application.remark }}</p>
      </div>

      <div style="float:right; margin-bottom: 15px" v-hasPermi="['safe:application:check']"
           v-if="this.whetherOrNotToAudit">
        <el-row :gutter="20" class="check_for" v-if=" gv('checkOn',application,null)=='wait'">
          <el-col :span="1.5">
            <el-button type="primary" @click="checkPass()">审核通过</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button @click="open = true;">审核不通过</el-button>
          </el-col>
        </el-row>
      </div>
      <div :style="tabName === 'firewallNat' ? { flex: 1, overflow: 'auto' } : { flex: 1 }" v-if="dialogVisible">
        <el-tabs v-model="tabName" type="card" :before-leave="changeTab" v-if="title.includes('查看')">
          <el-tab-pane label="系统详情" name="base"/>
          <el-tab-pane label="业务系统漏洞" name="webBusinessGap" v-if="isShowGap"/>
          <el-tab-pane label="端口暴露面" name="firewallNat" v-if="isShowGap"/>
        </el-tabs>
        <SystemDetails
          v-show="!title.includes('查看')"
          v-if="tabName === 'base'"
          ref="base"
          :asset-list="assetList"
          :asset-id="assetId"
          :changeId="changeId"/>
        <OperationSystemDetails
          v-show="title.includes('查看') && tabName  === 'base'"
          :asset-list="assetList"
          :asset-id="assetId"
          :changeId="changeId"/>
        <business-gap v-if="tabName === 'webBusinessGap' && isShowGap" :asset-id="assetId"/>
        <firewall-nat-index
          v-if="tabName === 'firewallNat'"
          style="height: calc(100% - 10px); display: flex; flex-direction: column"
          :current-application-id="assetId"
          :hidden-columns="['serverList','businessApplicationList','tools']"/>
      </div>

      <el-dialog title="业务应用审核" :visible.sync="open" width="600px" append-to-body>
        <el-form ref="form" label-width="130px">
          <div style="font-size: 22px;">不通过审核意见</div>
          <br>
          <div>
            <el-input v-model="content" :autosize="{minRows: 4, maxRows: 6}" type="textarea" placeholder="请输入意见"/>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="checkFail()">确定</el-button>
          <el-button @click="open=false;">取 消</el-button>
        </div>
      </el-dialog>

    </div>
    <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="handleAfter"
          v-if="!title.includes('查看')">
          保存
        </el-button>
<!--        <el-button
          type="primary"
          @click="handleSave"
          v-if="gv('checkOn',application)!='wait' && showData.value && ['component'].includes(tabName)">
          保存
        </el-button>
        <el-button
          v-if="['business','base'].includes(tabName) && !isShowGap" type="primary" @click="handleAfter"
                   :loading="afterBtnLoad">下一步
        </el-button>-->
    <el-button @click="handleClose">关 闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {getValFromObject} from "@/utils";
import {getApplication, checkApplication,auditConfig} from "@/api/safe/application";
import assetRegister from "@/mixins/assetRegister";

export default {
  name: "applicationInfo",
  mixins: [assetRegister],
  components: {
    SystemDetails: () => import('@/views/hhlCode/component/systemDetails'),
    FirewallNatIndex: () => import('@/views/safe/safety/firewallNatIndex'),
    BusinessGap: () => import('../businessGap'),
    ApplicationForm: () => import('@/views/hhlCode/component/application/applicationForm'),
    ApplicationHardware: () => import('@/views/hhlCode/component/application/applicationHardware'),
    businessApplicationStatus: () => import('@/views/hhlCode/component/businessApplicationStatus'),
    OperationSystemDetails: () => import('@/views/hhlCode/component/OperationSystemDetails'),
  },
  props: {
    applicationVisible: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => {},
    },
  },
  provide() {
    return {
      $editable: this.showData
    }
  },
  data() {
    return {
      tabName: 'base',
      showData: {value: true},
      editable: false,
      assetId: null,
      application: null,
      open: false,
      content: null,
      ref: ['base', 'business', 'component'],//提交组件
      gv: getValFromObject,
      rules: {},
      whetherOrNotToAudit: false,
      isShowGap: true,
      afterBtnLoad: false,
      isTempSave: false,
      assetAllocationType: '1',
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.applicationVisible
      },
      set(value) {
        this.$emit('update:applicationVisible', value)
      }
    }
  },
  created() {
    auditConfig({
      pageNum: 1,
      pageSize: 10
    }).then(response => {
      this.whetherOrNotToAudit = response.rows[0].configValue !== 'false';
    })
  },
  mounted() {},

  methods: {
    changeId(id) {
      this.assetId = id;
    },
    // 保存
    // async handleSave() {
    //   await this.$refs[this.tabName].handleSave().then(()=>{
    //     this.$emit('applicationChange', true);
    //   });
    // },

    // 下一步（保存）
    async handleAfter() {
      // if (this.isShowGap) {
      //   if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1];
      //   return true;
      // }
      if (!this.assetId) {
        // 新增时，检验基本信息必填项
        if (!this.$refs['base'].validateForm()) return this.$modal.msgError("请先录入基本信息!");
      } else {
        // 编辑时，检验基本信息必填项
        if (this.tabName === 'base') if (!this.$refs['base'].validateForm()) return this.$modal.msgError("请先录入基本信息!");
      }
      this.afterBtnLoad = true;
      await this.$refs[this.tabName].handleSave().then(()=>{
        this.$emit('applicationChange', false);
      }).finally(() => {
        this.afterBtnLoad = false;
        this.$emit('deptSelectKeyChange', true);
      });
      // if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1]
    },
    async handleAfter2() {
      // if (this.isShowGap) {
      //   if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1];
      //   return true;
      // }
      if (!this.assetId) {
        // 新增时，检验基本信息必填项
        if (!this.$refs['base'].validateForm()) return this.$modal.msgError("请先录入基本信息!");
      } else {
        // 编辑时，检验基本信息必填项
        if (this.tabName === 'base') if (!this.$refs['base'].validateForm()) return this.$modal.msgError("请先录入基本信息!");
      }
      this.afterBtnLoad = true;
      await this.$refs[this.tabName].handleSave().then(()=>{
        this.isTempSave = true;
      }).finally(() => {
        this.afterBtnLoad = false;
      });
      // if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1]
    },
    changeTab(newName, oldName) {
      if (this.assetId == null && newName !== 'base') {
        this.$modal.msgError("请先录入系统基本信息，保存后再切换!");
        this.tabName = 'base';
        return false;
      }
      let components = ['base', 'business', 'component'];
      this.step = components.findIndex(item => item === newName)
    },
    checkPass() {
      let data = {assetId: this.assetId, operator: "pass"}
      this.$modal.confirm('是否确认要审核通过该项填报？').then(function () {
        checkApplication(data);
      }).then(() => {
        this.application.checkOn = 'pass';
        this.$modal.msgSuccess('已经完成审核！');
      });
    },
    checkFail() {
      if (this.content != null && this.content !== '') {
        if (this.content.length > 255) {
          this.$message.error("字符大小超过255！");
          return;
        }
        let data = {assetId: this.assetId, operator: "fail", content: this.content}
        checkApplication(data).then(res => {
          this.application.checkOn = 'fail';
          this.open = false;
          this.$modal.msgSuccess('已经完成审核！');
        });
      } else {
        this.$message.error("请输入不通过审核意见！");

      }
    },

    // 打开弹窗
    openDialog() {
      // if (this.params && this.params.assetId) {
      //   this.handleIntoPage();
      //   return;
      // }
      this.isShowGap = this.params.isShowGap !== undefined
      if (this.params.assetId && !this.assetId) {
        this.assetId = this.params.assetId;
        this.showData.value = this.params.showData ? this.params.showData === "true" : true;
        getApplication(this.assetId).then(res => {
          this.application = res.data.applicationVO;
        });
        if (this.params.details && this.params.tabName) {
          this.tabName = this.params.tabName;
        }
      }
    },

    // 关闭弹窗事件
    closeDialog() {
      this.assetId = null;
      this.tabName = 'base';
      this.showData.value = true;
      this.$emit('close')
    },

    // 关闭弹窗
    handleClose() {
      this.showData.value = true;
      this.dialogVisible = false;
    }
  }
}
</script>

<style scoped lang="scss">
.check_for {
  z-index: 9;
  position: relative;
}

.down {
  width: 100%;
  margin-top: 10px;
  text-align: center;
}

::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background: rgba(48, 111, 229, 0.8);
  color: #FFFFFF;
}

.m_mark {
  width: 100%;
  border: solid 1px red;
  border-radius: 10px;
  height: 80px;
  padding: 5px;
  margin-bottom: 10px;
}

::v-deep .el-tabs {
  height: 100%;

  .el-tabs__content {
    height: calc(100% - 56px);

    .el-tab-pane {
      height: 100%;
    }
  }
}

::v-deep .el-dialog__body {
  padding: 0 0 10px 20px;
}

.dialog-body {
  height: 700px;
  overflow: auto;
}

</style>
