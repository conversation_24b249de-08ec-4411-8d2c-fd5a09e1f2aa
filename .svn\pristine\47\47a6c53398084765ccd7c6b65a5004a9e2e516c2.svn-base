import request from '@/utils/request'

// 查询攻击者视角告警列列表
export function listAttackAlarm(query) {
  return request({
    url: '/threaten/AttackAlarm/list',
    method: 'get',
    params: query
  })
}

// 查询攻击者视角告警列详细
export function getAttackAlarm(id) {
  return request({
    url: '/threaten/AttackAlarm/' + id,
    method: 'get'
  })
}

// 查询攻击目标IP列表
export function getDipDetails(query) {
  return request({
    url: '/threaten/AttackAlarm/getDipDetails',
    method: 'get',
    params: query
  })
}

// 查询告警类型列表
export function getThreatenNameDetails(query) {
  return request({
    url: '/threaten/AttackAlarm/getThreatenNameDetails',
    method: 'get',
    params: query
  })
}

export function handleVuln(data) {
  return request({
    url: '/threaten/AttackAlarm/handle',
    method: 'post',
    data: data
  })
}

export function handleBatchVuln(data) {
  return request({
    url: '/threaten/AttackAlarm/handleBatch',
    method: 'post',
    data: data
  })
}

export function groupAlarmLevelStatistics(query) {
  return request({
    url: '/threaten/AttackAlarm/groupAlarmLevelStatistics',
    method: 'get',
    params: query
  })
}

// 新增攻击者视角告警列
export function addAttackAlarm(data) {
  return request({
    url: '/threaten/AttackAlarm',
    method: 'post',
    data: data
  })
}

// 修改攻击者视角告警列
export function updateAttackAlarm(data) {
  return request({
    url: '/threaten/AttackAlarm',
    method: 'put',
    data: data
  })
}

// 删除攻击者视角告警列
export function delAttackAlarm(id) {
  return request({
    url: '/threaten/AttackAlarm/' + id,
    method: 'delete'
  })
}
