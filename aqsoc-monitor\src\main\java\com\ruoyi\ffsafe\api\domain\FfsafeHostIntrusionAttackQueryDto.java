package com.ruoyi.ffsafe.api.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 主机入侵攻击查询DTO
 * 用于分页查询主机入侵攻击事件，支持多种查询条件
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FfsafeHostIntrusionAttackQueryDto extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 攻击源IP - 支持精确匹配查询 */
    private String sip;

    /** 目标IP - 支持精确匹配查询 */
    private String dip;

    /** 目标IP主机名 - 支持模糊查询 */
    private String dipName;

    /** 告警名称 - 支持模糊查询 */
    private String alertName;

    /** 处置状态: 0=未处置,1=已处置,2=忽略 - 精确匹配查询 */
    private Integer handleState;

    /** 所属探针（设备配置ID） - 精确匹配查询 */
    private Long deviceConfigId;

    /** 处置人 - 支持模糊查询 */
    private String disposer;

    /** 非凡返回的ID - 精确匹配查询 */
    private Integer ffId;
}
