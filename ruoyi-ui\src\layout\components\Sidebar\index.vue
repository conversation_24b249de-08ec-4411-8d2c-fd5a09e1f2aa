<template>
  <div :class="{'has-logo':showLogo}">
<!--       :style="{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground,-->
<!--           background: settings.sideTheme === 'theme-dark' ? '' : variables.menuGradient }">-->
<!--    <logo v-if="showLogo" :collapse="isCollapse"/>-->
    <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
      <!--          :background-color="settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground"-->
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path  + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
    <div class="sidebar-bottom" v-if="!isCollapse">
      <div class="text">
        <p><span>当前版本：2.5.3</span><span style="display: none;">Date: 2025-08-12 17:24 SvnVersion: $Revision: 3115 $</span></p>
        <p><span>技术支持：安盟科技</span></p>
        <p><span>联系方式：4001-110-9963</span></p>
      </div>
    </div>
  </div>
</template>

<script>
import {mapGetters, mapState} from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.scss";

export default {
  components: {SidebarItem, Logo},
  mounted() {},
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["sidebarRouters", "sidebar"]),
    activeMenu() {
      const route = this.$route;
      const {meta, path} = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      let activeMenu
      const parts = path.split('/');
      if (parts.length === 4 && parts[1] && parts[2] && parts[3]) {
        activeMenu = `/${parts[1]}/${parts[2]}`;
      }else{
        activeMenu =  path;
      }
      return activeMenu
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    // 侧边栏折叠状态
    isCollapse() {
      return !this.sidebar.opened;
    }
  }
};
</script>
<style scoped lang="scss">
.el-menu {
  background-color: unset;
  ::v-deep .el-submenu__title i {
    font-weight: 700;
  }
}
.sidebar-bottom {
  width: 190px;
  height: 72px;
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  border-top: 1px solid #7f7f7f24;
  .text{
    position: absolute;
    text-align: center;
    padding: 2px 2px 2px 2px;
    box-sizing: border-box;
    width: 100%;
    height: 54px;
    color: rgba(127, 127, 127, 0.4980392156862745);
    font-size: 12px;
    word-wrap: break-word;
    text-transform: none;
  }
}
</style>
