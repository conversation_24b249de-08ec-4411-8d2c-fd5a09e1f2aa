import request from '@/utils/request'

// 获取数据列表（分页）
export function getList(data) {
    return request({
        url: `/api/work-hw/getList`,
        method: 'post',
        data
    })
}
// 获取下拉数据列表（不分页）
export function getSelect(data) {
    return request({
        url: `/api/work-hw/getList`,
        method: 'post',
        data
    })
}
// 获取数据详情
export function getInfo(id) {
    return request({
        url: `/api/work-hw/${id}`,
        method: 'get'
    })
}
export function getCountNum(countId) {
  return request({
    url: `/api/work-hw/getCountNum?countId=${countId}`,
    method: 'get'
  })
}
//新增数据
export function addData(_data){
    return request({
        url: '/api/work-hw',
        method: 'POST',
        data: _data
    })
}
//修改数据
export function editData(_data){
    return request({
        url: '/api/work-hw',
        method: 'PUT',
        data: _data
    })
}
// 删除数据
export function delData(id){
    return request({
        url: `/api/work-hw/${id}`,
        method: 'DELETE'
    })
}
export function getNum(path,id) {
  return request({
    url: `/api/work-hw/${path}?id=${id}`,
    method: 'get'
  })
}
export function getUserNames(userIds) {
  return request({
    url: `/api/work-hw/getUserNames?userIds=${userIds}`,
    method: 'get'
  })
}
export function updateStages(_data){
  return request({
    url: '/api/work-hw/updateStages',
    method: 'POST',
    data: _data
  })
}

//复制
export function copyWork(_data){
  return request({
    url: '/api/work-hw/copyWork',
    method: 'POST',
    data: _data
  })
}
