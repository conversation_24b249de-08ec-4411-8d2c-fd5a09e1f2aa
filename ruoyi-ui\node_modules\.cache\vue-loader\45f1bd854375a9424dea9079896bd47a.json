{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\application\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\application\\index.vue", "mtime": 1756294198410}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZGVsQXBwbGljYXRpb24sCiAgbGlzdEFwcGxpY2F0aW9uLAogIGFwcGx5QXBwbGljYXRpb24sCiAgZ2V0QXBwQ291bnRCeURpY3QsCiAgYXVkaXRDb25maWcKfSBmcm9tICdAL2FwaS9zYWZlL2FwcGxpY2F0aW9uJwppbXBvcnQgIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QuY3NzIjsKaW1wb3J0IHtjaGVja1Blcm1pLCBjaGVja1JvbGV9IGZyb20gIkAvdXRpbHMvcGVybWlzc2lvbiI7CmltcG9ydCB7bWFwR2V0dGVyc30gZnJvbSAndnVleCcKaW1wb3J0IHtnZXRUb2tlbn0gZnJvbSAiQC91dGlscy9hdXRoIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQXBwbGljYXRpb24iLAogIGRpY3RzOiBbCiAgICAncHJvdGVjdGlvbl9ncmFkZScsCiAgICAnYXBwY2hlY2tfc3RhdGUnLAogICAgJ2FwcF9sb2dpbl90eXBlJywKICAgICdhcHBfZGVwbG95JywKICAgICdzZXJ2ZV9ncm91cCcsCiAgICAnYXBwX3N0YXRlJywKICAgICdjb25zdHJ1Y3RfdHlwZScsCiAgICAnc3lzdGVtX3R5cGUnLAogICAgJ2Fzc2V0X3N0YXRlJywKICAgICdwcm90ZWN0aW9uX2dyYWRlJywKICAgICdhcHBjaGVja19zdGF0ZScKICBdLAogIGNvbXBvbmVudHM6IHsKICAgIEFwcGxpY2F0aW9uRGV0YWlsczogKCkgPT4gaW1wb3J0KCdAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uRGV0YWlscy52dWUnKSwKICAgIEFwcGxpY2F0aW9uRGlhbG9nOiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvaGhsQ29kZS9jb21wb25lbnQvYXBwbGljYXRpb24vYXBwbGljYXRpb25JbmZvLnZ1ZScpLAogICAgaW1wb3J0VGhyZWF0ZW5JbmZvOiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvYmFzaXMvc2VjdXJpdHlXYXJuL2ltcG9ydFRocmVhdGVuSW5mbycpLAogICAgRGVwdFNlbGVjdFN5c3RlbTogKCkgPT4gaW1wb3J0KCdAL3ZpZXdzL3NhZmUvYXBwbGljYXRpb24vY29tcG9uZW50L2RlcHRTZWxlY3RTeXN0ZW0nKSwKICAgIHR5cGVUcmVlOiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvY29tcG9uZW50cy90eXBlVHJlZScpLAogICAgdmVuZG9yU2VsZWN0OiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvdmVuZG9yU2VsZWN0JyksCiAgICB1cGxvYWRGaWxlVGFibGU6ICgpID0+IGltcG9ydCgnQC92aWV3cy9jb21wb25lbnRzL3RhYmxlL3VwbG9hZEZpbGVUYWJsZScpLAogICAgU3lzdGVtTGlzdDogKCkgPT4gaW1wb3J0KCcuLi8uLi8uLi9jb21wb25lbnRzL1N5c3RlbUxpc3QnKQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHNob3dBbGw6IGZhbHNlLAogICAgICB1cGxvYWQ6IHsKICAgICAgICBvcGVuOiBmYWxzZSwgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yI55So5oi35a+85YWl77yJCiAgICAgICAgdGl0bGU6ICIiLCAvLyDlvLnlh7rlsYLmoIfpopjvvIjnlKjmiLflr7zlhaXvvIkKICAgICAgICBjbGVhcjogIjAiLAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwgLy8g5piv5ZCm56aB55So5LiK5LygCiAgICAgICAgaGVhZGVyczoge0F1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCl9LCAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL3NhZmUvYXBwbGljYXRpb24vaW1wb3J0RGF0YVRvSmlhbmdUb25nIiwgLy8g5LiK5Lyg55qE5Zyw5Z2ACiAgICAgIH0sCiAgICAgIHdoZXRoZXJPck5vdFRvQXVkaXQ6IGZhbHNlLAogICAgICBjaGVja1Blcm1pOiBjaGVja1Blcm1pLAogICAgICBjaGVja1JvbGU6IGNoZWNrUm9sZSwKICAgICAgY29udGVudDogIiIsCiAgICAgIGNsYXNzSWQ6IDcsCiAgICAgIGNsYXNzTmFtZTogJ+S4muWKoeW6lOeUqOezu+e7ny/lubPlj7AnLAogICAgICB0eXBlbGlzdDogW10sCiAgICAgIGNoaWxkcmVuOiBbXSwKICAgICAgbG9hZGluZzogdHJ1ZSwgLy8g6YGu572p5bGCCiAgICAgIGlkczogW10sIC8vIOmAieS4reaVsOe7hAogICAgICBjdXJyZW50TmFtZXM6IFtdLAogICAgICBhc3NldE5hbWVzOiBbXSwgLy8g6YCJ5Lit6KGo5pWw57uECiAgICAgIHNpbmdsZTogdHJ1ZSwgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLCAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHRvdGFsOiAwLCAvLyDmgLvmnaHmlbAKICAgICAgYXBwbGljYXRpb25MaXN0OiBbXSwgLy8g5Lia5Yqh5bqU55So57O757uf6KGo5qC85pWw5o2uCiAgICAgIHRpdGxlOiAiIiwgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIG9wZW46IGZhbHNlLCAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgcGFyYW1zOiB7fSwKICAgICAgZGVwdFNlbGVjdEtleTogMCwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgaXNBc2M6ICdkZXNjJywKICAgICAgICBvcmRlckJ5Q29sdW1uOiAnY3JlYXRlVGltZScsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgYXNzZXRDb2RlOiBudWxsLAogICAgICAgIGFzc2V0TmFtZTogbnVsbCwKICAgICAgICBkZWdyZWVJbXBvcnRhbmNlOiBudWxsLAogICAgICAgIGRvbWFpbklkOiBudWxsLAogICAgICAgIGFwcGxpY2F0aW9uSWRzOltdCiAgICAgIH0sCiAgICAgIHN5c3RlbXNUeXBlOiBbXSwgLy8g57O757uf57G75Z6LCiAgICAgIGFzc2V0U3RhdGU6IFtdLCAvLyDkuIrnur/nirbmgIEKICAgICAgcHJvdGVjdGlvbkdyYWRlOiBbXSwgLy8g562J5L+d562J57qnCiAgICAgIGFwcGNoZWNrU3RhdGU6IFtdLCAvLyDlrqHmoLjnirbmgIEKICAgICAgcGFyYW1zQXJyYXk6IFsnc3lzdGVtX3R5cGUnLCAnYXNzZXRfc3RhdGUnLCAncHJvdGVjdGlvbl9ncmFkZScsICdhcHBjaGVja19zdGF0ZSddLCAvLyDmkJzntKLlj4LmlbAKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgcmVtYXJrTXNnOiBbCiAgICAgICAgICB7IG1pbjogMywgbWF4OiAxNzAsIG1lc3NhZ2U6ICfkv67mlLnorrDlvZXkuI3og73lsJHkuo4z5Liq5a2X56ym5LiU5LiN6IO95aSn5LqOMTcw5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5L+u5pS56K6w5b2V5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgIF0KICAgICAgfSwKICAgICAgY29sdW1uczogWwogICAgICAgIHtrZXk6IDAsIGxhYmVsOiAi57O757uf5ZCN56ewIiwgdmlzaWJsZTogdHJ1ZX0sCiAgICAgICAge2tleTogMSwgbGFiZWw6ICLnmbvlvZXlnLDlnYAiLCB2aXNpYmxlOiB0cnVlfSwKICAgICAgICB7a2V5OiAyLCBsYWJlbDogIuaJgOWxnumDqOmXqCIsIHZpc2libGU6IHRydWV9LAogICAgICAgIHtrZXk6IDMsIGxhYmVsOiAi6LSj5Lu75Lq65ZGYIiwgdmlzaWJsZTogdHJ1ZX0sCiAgICAgICAge2tleTogNCwgbGFiZWw6ICLogZTns7vnlLXor50iLCB2aXNpYmxlOiBmYWxzZX0sCiAgICAgICAge2tleTogNSwgbGFiZWw6ICLnrYnkv53nuqfliKsiLCB2aXNpYmxlOiB0cnVlfSwKICAgICAgICB7a2V5OiA2LCBsYWJlbDogIuWFs+mUruiuvuaWvSIsIHZpc2libGU6IGZhbHNlfSwKICAgICAgICB7a2V5OiA3LCBsYWJlbDogIuaKgOacr+aetuaehCIsIHZpc2libGU6IGZhbHNlfSwKICAgICAgICB7a2V5OiA4LCBsYWJlbDogIueZu+W9leaWueW8jyIsIHZpc2libGU6IGZhbHNlfSwKICAgICAgICB7a2V5OiA5LCBsYWJlbDogIumDqOe9suaWueW8jyIsIHZpc2libGU6IGZhbHNlfSwKICAgICAgICB7a2V5OiAxMCwgbGFiZWw6ICLkvpvlupTllYYiLCB2aXNpYmxlOiBmYWxzZX0sCiAgICAgICAge2tleTogMTEsIGxhYmVsOiAi5pyN5Yqh5a+56LGhIiwgdmlzaWJsZTogZmFsc2V9LAogICAgICAgIHtrZXk6IDEyLCBsYWJlbDogIuaOiOadg+eUqOaIt+aVsCIsIHZpc2libGU6IGZhbHNlfSwKICAgICAgICB7a2V5OiAxMywgbGFiZWw6ICLmnIjlnYfmtLvot4PkurrmlbAiLCB2aXNpYmxlOiBmYWxzZX0sCiAgICAgICAge2tleTogMTQsIGxhYmVsOiAi5LiK57q/54q25oCBIiwgdmlzaWJsZTogdHJ1ZX0sCiAgICAgICAge2tleTogMTUsIGxhYmVsOiAi5a6h5qC454q25oCBIiwgdmlzaWJsZTogdHJ1ZX0sCiAgICAgICAge2tleTogMTYsIGxhYmVsOiAi5aSH5rOoIiwgdmlzaWJsZTogZmFsc2V9LAogICAgICAgIHtrZXk6IDE3LCBsYWJlbDogIuagh+etviIsIHZpc2libGU6IGZhbHNlfSwKICAgICAgICB7a2V5OiAxOCwgbGFiZWw6ICLkuKXph43mvI/mtJ4iLCB2aXNpYmxlOiB0cnVlfSwKICAgICAgICB7a2V5OiAxOSwgbGFiZWw6ICLpq5jljbHmvI/mtJ4iLCB2aXNpYmxlOiB0cnVlfSwKICAgICAgICB7a2V5OiAyMCwgbGFiZWw6ICLkuK3ljbHmvI/mtJ4iLCB2aXNpYmxlOiB0cnVlfSwKICAgICAgICB7a2V5OiAyMSwgbGFiZWw6ICJJUOWcsOWdgCIsIHZpc2libGU6IHRydWV9LAogICAgICBdLAogICAgICBlZGl0SXRlbTogImVkaXQiLAogICAgICBlZGl0YWJsZTogdHJ1ZSwKICAgICAgc3RlcDogMCwKICAgICAgY3VycmVudENvbXBvbmVudDogJ0FkZEFwcCcsCiAgICAgIGF1ZGl0QXBwOiBudWxsLAogICAgICByZW1hcmtGcm9tOiB7CiAgICAgICAgcmVtYXJrTXNnOiAnJywKICAgICAgfSwKICAgICAgcmVtYXJrRGlhbG9nOiBmYWxzZSwKICAgICAgZGVmYXVsdFNob3c6IHRydWUsCiAgICAgIGFwcGxpY2F0aW9uVmVyc2lvbjogZmFsc2UsCiAgICAgIGFwcGxpY2F0aW9uVmlzaWJsZTogZmFsc2UsCiAgICAgIG9wdGlvbkNvbHVtbnM6IFsi57O757uf5ZCN56ewIiwgIueZu+W9leWcsOWdgCIsICJJUOWcsOWdgCIsICAi5omA5bGe6YOo6ZeoIiwgIui0o+S7u+S6uuWRmCIsICLogZTns7vnlLXor50iLCAi562J5L+d57qn5YirIiwgIuWFs+mUruiuvuaWvSIsICLmioDmnK/mnrbmnoQiLCAi55m75b2V5pa55byPIiwgIumDqOe9suaWueW8jyIsICLkvpvlupTllYYiLCAi5pyN5Yqh5a+56LGhIiwgIuaOiOadg+eUqOaIt+aVsCIsICLmnIjlnYfmtLvot4PkurrmlbAiLCAi5LiK57q/54q25oCBIiwgIuWuoeaguOeKtuaAgSIsICLlpIfms6giLCAi5qCH562+IiwgIuS4pemHjea8j+a0niIsIumrmOWNsea8j+a0niIsIuS4reWNsea8j+a0niJdLAogICAgICBjaGVja2VkQ29sdW1uczogWyLns7vnu5/lkI3np7AiLCAi5omA5bGe6YOo6ZeoIiwgIui0o+S7u+S6uuWRmCIsICLnmbvlvZXlnLDlnYAiLCAgIklQ5Zyw5Z2AIiwgIuetieS/nee6p+WIqyIsICLkuIrnur/nirbmgIEiLCAi5a6h5qC454q25oCBIiwgIuS4pemHjea8j+a0niIsIumrmOWNsea8j+a0niIsIuS4reWNsea8j+a0niJdLAogICAgICB0YWJsZUtleTogMSwKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAuLi5tYXBHZXR0ZXJzKFsiYWN0aXZlTmFtZXMiXSksCiAgfSwKICBjcmVhdGVkKCkgewogICAgYXVkaXRDb25maWcoewogICAgICBwYWdlTnVtOiAxLAogICAgICBwYWdlU2l6ZTogMTAKICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICB0aGlzLndoZXRoZXJPck5vdFRvQXVkaXQgPSByZXNwb25zZS5yb3dzWzBdLmNvbmZpZ1ZhbHVlICE9PSAnZmFsc2UnOwogICAgfSkKICAgIHRoaXMuaW5pdFNlYXJjaERhdGFBbmRMaXN0RGF0YSgpOwogICAgLy8gdGhpcy5oYW5kbGVRdWVyeSgpOwogIH0sCiAgd2F0Y2g6IHsKICAgICckcm91dGVyLm5hbWUnKHZhbCkgewogICAgICBpZiAodmFsID09ICdBcHBsaWNhdGlvbicpIHsKICAgICAgICB0aGlzLmluaXQoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgICckcm91dGUucXVlcnknOiB7CiAgICAgIGhhbmRsZXIodmFsKSB7CiAgICAgICAgaWYodmFsICYmIHZhbC5kZXB0SWQpewogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQgPSBwYXJzZUludCh2YWwuZGVwdElkKTsKICAgICAgICB9CiAgICAgICAgaWYgKHZhbCAmJiB2YWwuZG9tYWluSWQpewogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kb21haW5JZCA9IHZhbC5kb21haW5JZDsKICAgICAgICB9CiAgICAgICAgaWYgKHZhbCAmJiB2YWwuYXBwbGljYXRpb25JZHMpewogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hcHBsaWNhdGlvbklkcyA9IHZhbC5hcHBsaWNhdGlvbklkczsKICAgICAgICB9CiAgICAgICAgaWYgKHZhbCAmJiB2YWwuc3lzdGVtVHlwZSl7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN5c3RlbVR5cGUgPSB2YWwuc3lzdGVtVHlwZTsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUsCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLy8g5a+85YWl5Yqf6IO9CiAgICBoYW5kbGVJbXBvcnQoKSB7CiAgICAgIHRoaXMudXBsb2FkLnRpdGxlID0gIuS4muWKoeezu+e7n+WvvOWFpSI7CiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSB0cnVlOwogICAgfSwKICAgIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlOwogICAgfSwKICAgIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKQogICAgICAgIHRoaXMuJGFsZXJ0KCLmiJDlip/lr7zlhaUiLCAi5a+85YWl57uT5p6cIiwge2Rhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZX0pOwogICAgICBlbHNlCiAgICAgICAgdGhpcy4kYWxlcnQocmVzcG9uc2UubXNnLCAi5a+85YWl57uT5p6cIiwge2Rhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZX0pOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgdGhpcy5kZXB0U2VsZWN0S2V5ICs9IDE7CiAgICB9LAogICAgc3VibWl0RmlsZUZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfSwKICAgIGltcG9ydFRlbXBsYXRlKCkgewogICAgICB0aGlzLmRvd25sb2FkKCdzYWZlL2FwcGxpY2F0aW9uL2ltcG9ydFRlbXBsYXRlVG9KaWFuZ1RvbmcnLCB7fSwgYGFwcGxpY2F0aW9uXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfSwKCiAgICAvLyDliJ3lp4vljJYKICAgIGFzeW5jIGluaXRTZWFyY2hEYXRhQW5kTGlzdERhdGEoKSB7CiAgICAgIGF3YWl0IHRoaXMuaW5pdFNlYXJjaERhdGEoKTsKICAgICAgdGhpcy5pbml0KCk7CiAgICB9LAoKICAgIC8vIOWIneWni+WMluafpeivouadoeS7tgogICAgYXN5bmMgaW5pdFNlYXJjaERhdGEoKSB7CiAgICAgIGZvciAobGV0IHBhcmFtcyBvZiB0aGlzLnBhcmFtc0FycmF5KSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0QXBwQ291bnRCeURpY3QocGFyYW1zKTsKICAgICAgICAgIGlmIChwYXJhbXMgPT09ICdzeXN0ZW1fdHlwZScpIHRoaXMuc3lzdGVtc1R5cGUgPSByZXNwb25zZS5kYXRhLmNvdW50QnlEaWN0TGlzdCB8fCBbXTsKICAgICAgICAgIGlmIChwYXJhbXMgPT09ICdhc3NldF9zdGF0ZScpIHRoaXMuYXNzZXRTdGF0ZSA9IHJlc3BvbnNlLmRhdGEuY291bnRCeURpY3RMaXN0IHx8IFtdOwogICAgICAgICAgaWYgKHBhcmFtcyA9PT0gJ3Byb3RlY3Rpb25fZ3JhZGUnKSB0aGlzLnByb3RlY3Rpb25HcmFkZSA9IHJlc3BvbnNlLmRhdGEuY291bnRCeURpY3RMaXN0IHx8IFtdOwogICAgICAgICAgaWYgKHBhcmFtcyA9PT0gJ2FwcGNoZWNrX3N0YXRlJykgdGhpcy5hcHBjaGVja1N0YXRlID0gcmVzcG9uc2UuZGF0YS5jb3VudEJ5RGljdExpc3QgfHwgW107CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ivt+axguWksei0pTonLCBlcnJvcik7CiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIGluaXQoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSBPYmplY3QuYXNzaWduKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuJHJvdXRlLnBhcmFtcyk7CiAgICAgIGlmICh0aGlzLiRyb3V0ZS5wYXJhbXMuYWRkKSB7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgdGhpcy5oYW5kbGVBZGQoKTsKICAgICAgICAgIHRoaXMuZm9ybS5sb2NhdGlvbklkID0gdGhpcy4kcm91dGUucGFyYW1zLmxvY2F0aW9uSWQ7CiAgICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIH0pCiAgICAgIH0KICAgIH0sCgogICAgLy/pgInkuK3pg6jpl6jkuovku7YKICAgIGRlcHRTZWxlY3Qobm9kZSkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmFzc2V0Q29kZSA9IG51bGw7CiAgICAgIC8vIHRoaXMucXVlcnlQYXJhbXMuYXNzZXROYW1lID0gbnVsbDsKICAgICAgaWYgKG5vZGUuaWQgIT0gbnVsbCkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gbm9kZS5pZDsKICAgICAgfQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAoKICAgIC8v5o6S5bqPCiAgICBzb3J0Q2hhbmdlKGNvbHVtbiwgcHJvcCwgb3JkZXIpIHsKICAgICAgaWYgKGNvbHVtbi5vcmRlciAhPSBudWxsKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc0FzYyA9ICdkZXNjJzsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzQXNjID0gJ2FzYyc7CiAgICAgIH0KICAgICAgaWYgKGNvbHVtbi5wcm9wID09ICdzdGF0ZScpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm9yZGVyQnlDb2x1bW4gPSAiZS5zdGF0ZSI7CiAgICAgIH0gZWxzZQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMub3JkZXJCeUNvbHVtbiA9IGNvbHVtbi5wcm9wOwogICAgICB0aGlzLmdldExpc3QodGhpcy5xdWVyeVBhcmFtcyk7CiAgICB9LAogICAgLyoqIOafpeivouS4muWKoeW6lOeUqOezu+e7n+WIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGlmKHRoaXMuJHJvdXRlLnBhcmFtcyl7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pZHMgPSB0aGlzLiRyb3V0ZS5wYXJhbXMuaWRzOwogICAgICB9CiAgICAgIGxpc3RBcHBsaWNhdGlvbih0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmFwcGxpY2F0aW9uTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5maW5hbGx5KCgpID0+IHsKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICB0aGlzLnRhYmxlS2V5Kys7CiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCgogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5lZGl0SXRlbSA9ICJlZGl0IjsKICAgICAgdGhpcy5lZGl0YWJsZSA9IHRydWU7CiAgICAgIHRoaXMuc3RlcCA9IDA7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3lzdGVtVHlwZSA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhdGUgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByb3RlY3RHcmFkZSA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2hlY2tPbiA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXNzZXRDb2RlID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hc3NldE5hbWUgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnVybCA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZG9tYWluVXJsID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kb21haW5JZCA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXBwbGljYXRpb25JZHMgPSBbXTsKICAgICAgdGhpcy4kcmVmcy5zeXN0ZW1MaXN0MSAmJiB0aGlzLiRyZWZzLnN5c3RlbUxpc3QxLnJlc2V0U2VsZWN0aW9uKCk7CiAgICAgIHRoaXMuJHJlZnMuc3lzdGVtTGlzdDIgJiYgdGhpcy4kcmVmcy5zeXN0ZW1MaXN0Mi5yZXNldFNlbGVjdGlvbigpOwogICAgICB0aGlzLiRyZWZzLnN5c3RlbUxpc3QzICYmIHRoaXMuJHJlZnMuc3lzdGVtTGlzdDMucmVzZXRTZWxlY3Rpb24oKTsKICAgICAgdGhpcy4kcmVmcy5zeXN0ZW1MaXN0NCAmJiB0aGlzLiRyZWZzLnN5c3RlbUxpc3Q0LnJlc2V0U2VsZWN0aW9uKCk7CiAgICAgIHRoaXMuY2xlYXJSb3V0ZVF1ZXJ5UGFyYW1zKCk7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICBjbGVhclJvdXRlUXVlcnlQYXJhbXMoKXsKICAgICAgaWYodGhpcy4kcm91dGUucGFyYW1zKXsKICAgICAgICBsZXQgcXVlcnlQYXJhbXMgPSB0aGlzLiRyb3V0ZS5wYXJhbXM7CiAgICAgICAgZGVsZXRlIHF1ZXJ5UGFyYW1zLmlkczsKICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7cGFyYW1zOiBxdWVyeVBhcmFtc30pCiAgICAgIH0KICAgIH0sCgogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uYXNzZXRJZCk7CiAgICAgIHRoaXMuYXNzZXROYW1lcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmFzc2V0TmFtZSk7CiAgICAgIHRoaXMuY3VycmVudE5hbWVzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uYXNzZXROYW1lKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKCiAgICAvL+aPkOS6pOWuoeaguAogICAgaGFuZGxlQXBwbHkoYXBwKSB7CiAgICAgIHRoaXMuYXVkaXRBcHAgPSBhcHAKICAgICAgaWYgKGFwcC5jaGVja0J5KSB7CiAgICAgICAgdGhpcy5yZW1hcmtEaWFsb2cgPSB0cnVlCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zdWJtaXRNc2coKQogICAgICB9CiAgICB9LAogICAgZm9ybU1zZ1N1Ym1pdCgpIHsKICAgICAgdGhpcy4kcmVmc1sncmVtYXJrRnJvbSddLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdGhpcy5zdWJtaXRNc2coKQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgc3VibWl0TXNnKCkgewogICAgICBhcHBseUFwcGxpY2F0aW9uKHsKICAgICAgICBhc3NldElkOiB0aGlzLmF1ZGl0QXBwLmFzc2V0SWQsCiAgICAgICAgcmVtYXJrOiB0aGlzLnJlbWFya0Zyb20ucmVtYXJrTXNnCiAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlt7Lnu4/mj5DkuqTlrqHmoLjvvIEiKTsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLmNhbGxPZmZNc2coKQogICAgICB9KQogICAgfSwKICAgIGNhbGxPZmZNc2coKSB7CiAgICAgIHRoaXMuYXVkaXRBcHAgPSBudWxsCiAgICAgIHRoaXMucmVtYXJrRnJvbS5yZW1hcmtNc2cgPSAnJwogICAgICB0aGlzLnJlbWFya0RpYWxvZyA9IGZhbHNlCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMudGl0bGUgPSAn5re75Yqg5bqU55So5L+h5oGvJzsKICAgICAgdGhpcy5wYXJhbXMgPSB7fTsKICAgICAgdGhpcy5hcHBsaWNhdGlvblZpc2libGUgPSB0cnVlOwogICAgICAvLyB0aGlzLiR0YWIub3BlblBhZ2UoCiAgICAgIC8vICAgIua3u+WKoOW6lOeUqOS/oeaBryIsCiAgICAgIC8vICAgIi9hc3NldC1sZWRnZXIvbW9uaXRvcjIvYXBwbGljYXRpb24vaW5mbyIsCiAgICAgIC8vICAge30KICAgICAgLy8gKTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93LCBlZGl0ID0gdHJ1ZSwgZGF0YSkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGlmIChyb3cuY2hlY2tPbiA9PT0gInBhc3MiICYmIChkYXRhID09PSB1bmRlZmluZWQgfHwgZGF0YSA9PT0gbnVsbCkpIHsKICAgICAgICB0aGlzLnRpdGxlID0gJ+S/ruaUueW6lOeUqOS/oeaBrycKICAgICAgICB0aGlzLmVkaXRhYmxlID0gZWRpdAogICAgICAgIGNvbnN0IGFzc2V0SWQgPSByb3cuYXNzZXRJZCB8fCB0aGlzLmlkcwogICAgICAgIHRoaXMucGFyYW1zID0ge2Fzc2V0SWQsIC4uLmRhdGF9OwogICAgICAgIHRoaXMucGFyYW1zLmlzRWRpdCA9IHRydWU7CiAgICAgICAgdGhpcy5hcHBsaWNhdGlvblZpc2libGUgPSB0cnVlOwogICAgICAgIC8vIHRoaXMuJHRhYi5vcGVuUGFnZSh0aXRsZSwgJy9hc3NldC1sZWRnZXIvbW9uaXRvcjIvYXBwbGljYXRpb24vaW5mbycsIHBhcmFtcyk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy50aXRsZSA9IGRhdGEgPyBkYXRhLnNob3dEYXRhID09PSAnZmFsc2UnID8gJ+afpeeci+W6lOeUqOS/oeaBrycgOiAn5L+u5pS55bqU55So5L+h5oGvJyA6ICfkv67mlLnlupTnlKjkv6Hmga8nOwogICAgICAgIHRoaXMuZWRpdGFibGUgPSBlZGl0OwogICAgICAgIGNvbnN0IGFzc2V0SWQgPSByb3cuYXNzZXRJZCB8fCB0aGlzLmlkczsKICAgICAgICB0aGlzLnBhcmFtcyA9IHthc3NldElkLCAuLi5kYXRhfTsKICAgICAgICB0aGlzLmFwcGxpY2F0aW9uVmlzaWJsZSA9IHRydWU7CiAgICAgICAgLy8gdGhpcy4kdGFiLm9wZW5QYWdlKHRpdGxlLCAnL2Fzc2V0LWxlZGdlci9tb25pdG9yMi9hcHBsaWNhdGlvbi9pbmZvJywge2Fzc2V0SWQsIC4uLmRhdGF9KTsKICAgICAgfQogICAgfSwKCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGFzc2V0SWRzID0gcm93LmFzc2V0SWQgfHwgdGhpcy5pZHM7CiAgICAgIGxldCBhc3NldHNOYW1lID0gIiI7CiAgICAgIGlmICghcm93LmFzc2V0SWQpIHsKICAgICAgICBhc3NldHNOYW1lID0gdGhpcy5jdXJyZW50TmFtZXMuam9pbigiLCIpOwogICAgICB9IGVsc2UgewogICAgICAgIGFzc2V0c05hbWUgPSByb3cuYXNzZXROYW1lOwogICAgICB9CgogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTjgJAnICsgYXNzZXRzTmFtZSArICfjgJHnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsQXBwbGljYXRpb24oYXNzZXRJZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLmRlcHRTZWxlY3RLZXkgKz0gMTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ3NhZmUvYXBwbGljYXRpb24vZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYGFwcGxpY2F0aW9uXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfSwKICAgIGFwcGxpY2F0aW9uQ2hhbmdlKGRhdGEpewogICAgICB0aGlzLmFwcGxpY2F0aW9uVmlzaWJsZSA9IGRhdGE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB0aGlzLmluaXRTZWFyY2hEYXRhKCk7CiAgICB9LAogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAui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file": "index.vue", "sourceRoot": "src/views/safe/application", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-tree-container\" ref=\"system\">\n      <dept-select-system\n        :key=\"deptSelectKey\"\n        ref=\"deptSelect\"\n        :is-current=\"true\"\n        @deptSelect=\"deptSelect\"\n        asset-class=\"application\"\n        :current-dept-id=\"queryParams.deptId\"\n      />\n    </div>\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          @submit.native.prevent\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"系统名称\" prop=\"assetName\">\n                <el-input\n                  v-model=\"queryParams.assetName\"\n                  placeholder=\"请输入系统名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"登录地址\">\n                <el-input v-model=\"queryParams.url\" clearable placeholder=\"请输入登录地址\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"域名\">\n                <el-input v-model=\"queryParams.domainUrl\" clearable placeholder=\"请输入域名\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"24\" v-if=\"systemsType.length\">\n              <el-form-item label=\"系统类型\">\n                <SystemList\n                  ref=\"systemList1\"\n                  :systemTypes=\"systemsType\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypeVal.sync=\"queryParams.systemType\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\" v-if=\"assetState.length\">\n              <el-form-item label=\"上线状态\">\n                <SystemList\n                  ref=\"systemList2\"\n                  :systemTypes=\"assetState\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypeVal.sync=\"queryParams.state\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\" v-if=\"protectionGrade.length\">\n              <el-form-item label=\"等保等级\">\n                <SystemList\n                  ref=\"systemList3\"\n                  paramVal=\"protectGrade\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypes=\"protectionGrade\"\n                  :systemTypeVal.sync=\"queryParams.protectGrade\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\" v-if=\"appcheckState.length\">\n              <el-form-item label=\"审核状态\">\n                <SystemList\n                  ref=\"systemList4\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypes=\"appcheckState\"\n                  :systemTypeVal.sync=\"queryParams.checkOn\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">业务系统列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleAdd\"\n                  v-hasPermi=\"['safe:application:add']\"\n                >新增</el-button\n                >\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  v-hasPermi=\"['safe:application:add']\"\n                  @click=\"handleImport\"\n                >导入</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"handleDelete\"\n                  v-hasPermi=\"['safe:application:remove']\"\n                >批量删除</el-button\n                >\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['safe:application:export']\"\n                >导出</el-button\n                >\n              </el-col>\n<!--              <right-toolbar-->\n<!--                :showSearch.sync=\"showSearch\"-->\n<!--                :columns=\"columns\"-->\n<!--                @queryTable=\"getList\"-->\n<!--              ></right-toolbar>-->\n            </el-row>\n          </div>\n        </div>\n        <div class=\"tableContainer\">\n          <el-table ref=\"elTable\"\n                    v-loading=\"loading\"\n                    height=\"100%\"\n                    :data=\"applicationList\"\n                    :key=\"tableKey\"\n                    @selection-change=\"handleSelectionChange\"\n                    @sort-change=\"sortChange\">\n            <el-table-column\n              type=\"selection\"\n              width=\"55\">\n            </el-table-column>\n            <el-table-column\n              label=\"系统名称\"\n              fixed=\"left\"\n              min-width=\"150\"\n              align=\"left\"\n              prop=\"assetName\"\n              v-if=\"columns[0].visible\"\n              :sortable=\"false\"\n              show-overflow-tooltip\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.assetName || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"信息完整度\"\n              min-width=\"150\"\n              prop=\"completeness\"\n            >\n              <template slot-scope=\"scope\">\n                <div style=\"display: flex; align-items: center;\">\n                  <el-progress\n                    :color=\"scope.row.completeness > 80 ? '#67c23a' : scope.row.completeness < 60 ? '#f56c6c' : '#e6a23c'\"\n                    :percentage=\"scope.row.completeness\"\n                    :show-text=\"false\"\n                    style=\"flex: 1;\"\n                  ></el-progress>\n                  <span style=\"margin-left: 10px; width: 50px;\">\n                    {{ scope.row.completenessStr }}%\n                  </span>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"所属部门\"\n              min-width=\"150\"\n              prop=\"deptName\"\n              v-if=\"columns[2].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.deptName || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"责任人员\"\n              width=\"120\"\n              prop=\"managerName\"\n              v-if=\"columns[3].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.managerName || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"联系电话\"\n              width=\"150\"\n              prop=\"managerPhone\"\n              :sortable=\"false\"\n              v-if=\"columns[4].visible\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.managerPhone || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"关键设施\"\n\n              width=\"120\"\n              prop=\"iskey\"\n              v-if=\"columns[6].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.iskey || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"登录地址\"\n              min-width=\"120\"\n              prop=\"url\"\n              v-if=\"columns[1].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.url || \"-\" }}\n<!--                <el-popover placement=\"left-end\" width=\"200\" trigger=\"hover\">\n                  <span>{{ scope.row.url || \"-\" }}</span>\n                  <span class=\"r_popover\" slot=\"reference\">\n                  {{ scope.row.url || \"-\" }}\n                </span>\n                </el-popover>-->\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"IP地址\"\n              width=\"120\"\n              prop=\"ip\"\n              v-if=\"columns[21].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.ip || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"等保级别\"\n              width=\"120\"\n              prop=\"protectGrade\"\n              v-if=\"columns[5].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.protection_grade\"\n                  :value=\"scope.row.protectGrade || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"技术架构\"\n              width=\"120\"\n              prop=\"technical\"\n              v-if=\"columns[7].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.construct_type\"\n                  :value=\"scope.row.construct || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"登录方式\"\n              width=\"100\"\n              prop=\"loginType\"\n              v-if=\"columns[8].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.app_login_type\"\n                  :value=\"scope.row.loginType || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"部署方式\"\n              width=\"100\"\n              prop=\"deploy\"\n              v-if=\"columns[9].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.app_deploy\"\n                  :value=\"scope.row.deploy || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"开发厂商\"\n              min-width=\"130\"\n              prop=\"vendorName\"\n              v-if=\"columns[10].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.vendorName || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"服务对象\"\n              width=\"100\"\n              prop=\"serviceGroup\"\n              v-if=\"columns[11].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.serve_group\"\n                  :value=\"scope.row.serviceGroup || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"授权用户数\"\n              width=\"120\"\n              prop=\"userNums\"\n              v-if=\"columns[12].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.vnlnUpdateTime || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"月均活跃人数\"\n              width=\"130\"\n              prop=\"everydayActiveNums\"\n              v-if=\"columns[13].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.everydayActiveNums || 0 }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"上线状态\"\n              width=\"100\"\n              prop=\"state\"\n              v-if=\"columns[14].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.asset_state\"\n                  :value=\"scope.row.state || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"审核状态\"\n              width=\"100\"\n              prop=\"checkOn\"\n              v-if=\"columns[15].visible && whetherOrNotToAudit\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.appcheck_state\"\n                  :value=\"scope.row.checkOn || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column label=\"严重漏洞\" align=\"left\" prop=\"criticalVulnCount\" width=\"100\">\n              <template slot-scope=\"scope\">\n                {{ scope.row.criticalVulnCount || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column label=\"高危漏洞\" align=\"left\" width=\"100\" prop=\"highVulnCount\">\n              <template slot-scope=\"scope\">\n                {{ scope.row.highVulnCount || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column label=\"中危漏洞\" align=\"left\" width=\"100\" prop=\"mediumVulnCount\">\n              <template slot-scope=\"scope\">\n                {{ scope.row.mediumVulnCount || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"备注\"\n              min-width=\"140\"\n              align=\"left\"\n              prop=\"remark\"\n              v-if=\"columns[16].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.remark }}\n<!--                <el-popover placement=\"right-end\" width=\"200\" trigger=\"hover\">\n                  <span>{{ scope.row.remark }}</span>\n                  <span class=\"r_popover\" slot=\"reference\">\n                  {{ scope.row.remark }}\n                </span>\n                </el-popover>-->\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"标签\"\n              min-width=\"140\"\n              align=\"left\"\n              prop=\"tags\"\n              v-if=\"columns[17].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-popover placement=\"right-end\" width=\"200\" trigger=\"hover\">\n                  <span>{{ scope.row.tags }}</span>\n                  <span class=\"r_popover\" slot=\"reference\">\n                  {{ scope.row.tags || \"-\" }}\n                </span>\n                </el-popover>\n              </template>\n            </el-table-column>\n            <el-table-column :key=\"Math.random()\" label=\"操作\" align=\"left\" fixed=\"right\" width=\"200\" :show-overflow-tooltip=\"false\">\n              <template slot=\"header\">\n                <ColumnFilter :optionColumns=\"optionColumns\" :checkedColumns.sync=\"checkedColumns\" :columns=\"columns\" />\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\"\n                           @click=\"handleUpdate(scope.row,true,{showData:'false', isShowGap: true},true)\"\n                           v-if=\"!whetherOrNotToAudit || (scope.row.checkOn!='new' && checkPermi(['safe:application:list']))\">\n                  详情\n                </el-button>\n                <el-button size=\"mini\" type=\"text\" @click=\"handleApply(scope.row)\"\n                           v-if=\"whetherOrNotToAudit && scope.row.checkOn == 'new' && checkPermi(['safe:application:apply'])\">\n                  提交审核\n                </el-button>\n                <el-button size=\"mini\" type=\"text\"\n                           @click=\"handleUpdate(scope.row,false,{showData:'false'})\"\n                           v-if=\"whetherOrNotToAudit && scope.row.checkOn=='wait'&& checkPermi(['safe:application:check'])\">\n                  审核\n                </el-button>\n<!--                <el-button size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\"\n                           v-if=\"scope.row.checkOn!='wait' && checkPermi(['safe:application:edit'])\">编辑\n                </el-button>\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDelete(scope.row)\" class=\"table-delBtn\"\n                           v-if=\" scope.row.checkOn!='wait' && checkPermi(['safe:application:remove'])\">删除\n                </el-button>-->\n                <el-button size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\"\n                           v-if=\"checkPermi(['safe:application:edit'])\">编辑\n                </el-button>\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDelete(scope.row)\" class=\"table-delBtn\"\n                           v-if=\"checkPermi(['safe:application:remove'])\">删除\n                </el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\n                    @pagination=\"getList\"/>\n      </div>\n    </div>\n\n    <el-dialog title=\"填写修改记录\" :visible.sync=\"remarkDialog\" width=\"600px\" append-to-body>\n      <el-form ref=\"remarkFrom\" :model=\"remarkFrom\" :rules=\"rules\">\n        <el-form-item prop=\"remarkMsg\">\n          <el-input type=\"textarea\" :rows=\"8\" minlength=\"3\" maxlength=\"170\"\n                    v-model.trim=\"remarkFrom.remarkMsg\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"formMsgSubmit\">提交</el-button>\n        <el-button @click=\"callOffMsg\">取消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".xlsx, .xls\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url\"\n        :disabled=\"upload.isUploading\"\n        :data=\"{'clear':upload.clear}\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">\n          将文件拖到此处，或\n          <em>点击上传</em>\n        </div>\n        <div class=\"el-upload__tip\" style=\"color: red\" slot=\"tip\">仅允许导入xls、xlsx格式文件。</div>\n      </el-upload>\n      <el-link\n        type=\"primary\"\n        :underline=\"false\"\n        style=\"font-size:12px;vertical-align: baseline;\"\n        @click=\"importTemplate\"\n      >下载模板\n      </el-link>\n      <!--      <el-checkbox v-model=\"upload.clear\" true-label=\"1\" false-label=\"0\">导入前清空</el-checkbox>-->\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n<!--    <application-dialog\n      :title=\"title\"\n      :params.sync=\"params\"\n      @applicationChange=\"applicationChange\"\n      :applicationVisible.sync=\"applicationVisible\"/>-->\n<!--业务系统新增交互改版-->\n    <application-details\n      :title=\"title\"\n      :params.sync=\"params\"\n      @applicationChange=\"applicationChange\"\n      @deptSelectKeyChange=\"deptSelectKey++\"\n      :applicationVisible.sync=\"applicationVisible\"/>\n  </div>\n</template>\n\n<script>\nimport {\n  delApplication,\n  listApplication,\n  applyApplication,\n  getAppCountByDict,\n  auditConfig\n} from '@/api/safe/application'\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport {checkPermi, checkRole} from \"@/utils/permission\";\nimport {mapGetters} from 'vuex'\nimport {getToken} from \"@/utils/auth\";\n\nexport default {\n  name: \"Application\",\n  dicts: [\n    'protection_grade',\n    'appcheck_state',\n    'app_login_type',\n    'app_deploy',\n    'serve_group',\n    'app_state',\n    'construct_type',\n    'system_type',\n    'asset_state',\n    'protection_grade',\n    'appcheck_state'\n  ],\n  components: {\n    ApplicationDetails: () => import('@/views/hhlCode/component/applicationDetails.vue'),\n    ApplicationDialog: () => import('@/views/hhlCode/component/application/applicationInfo.vue'),\n    importThreatenInfo: () => import('@/views/basis/securityWarn/importThreatenInfo'),\n    DeptSelectSystem: () => import('@/views/safe/application/component/deptSelectSystem'),\n    typeTree: () => import('@/views/components/typeTree'),\n    vendorSelect: () => import('@/views/components/select/vendorSelect'),\n    uploadFileTable: () => import('@/views/components/table/uploadFileTable'),\n    SystemList: () => import('../../../components/SystemList')\n  },\n  data() {\n    return {\n      showAll: false,\n      upload: {\n        open: false, // 是否显示弹出层（用户导入）\n        title: \"\", // 弹出层标题（用户导入）\n        clear: \"0\",\n        isUploading: false, // 是否禁用上传\n        headers: {Authorization: \"Bearer \" + getToken()}, // 设置上传的请求头部\n        url: process.env.VUE_APP_BASE_API + \"/safe/application/importDataToJiangTong\", // 上传的地址\n      },\n      whetherOrNotToAudit: false,\n      checkPermi: checkPermi,\n      checkRole: checkRole,\n      content: \"\",\n      classId: 7,\n      className: '业务应用系统/平台',\n      typelist: [],\n      children: [],\n      loading: true, // 遮罩层\n      ids: [], // 选中数组\n      currentNames: [],\n      assetNames: [], // 选中表数组\n      single: true, // 非单个禁用\n      multiple: true, // 非多个禁用\n      showSearch: true, // 显示搜索条件\n      total: 0, // 总条数\n      applicationList: [], // 业务应用系统表格数据\n      title: \"\", // 弹出层标题\n      open: false, // 是否显示弹出层\n      params: {},\n      deptSelectKey: 0,\n      // 查询参数\n      queryParams: {\n        isAsc: 'desc',\n        orderByColumn: 'createTime',\n        pageNum: 1,\n        pageSize: 10,\n        assetCode: null,\n        assetName: null,\n        degreeImportance: null,\n        domainId: null,\n        applicationIds:[]\n      },\n      systemsType: [], // 系统类型\n      assetState: [], // 上线状态\n      protectionGrade: [], // 等保等级\n      appcheckState: [], // 审核状态\n      paramsArray: ['system_type', 'asset_state', 'protection_grade', 'appcheck_state'], // 搜索参数\n      // 表单校验\n      rules: {\n        remarkMsg: [\n          { min: 3, max: 170, message: '修改记录不能少于3个字符且不能大于170个字符', trigger: 'blur' },\n          { required: true, message: '修改记录不能为空', trigger: 'blur'}\n        ]\n      },\n      columns: [\n        {key: 0, label: \"系统名称\", visible: true},\n        {key: 1, label: \"登录地址\", visible: true},\n        {key: 2, label: \"所属部门\", visible: true},\n        {key: 3, label: \"责任人员\", visible: true},\n        {key: 4, label: \"联系电话\", visible: false},\n        {key: 5, label: \"等保级别\", visible: true},\n        {key: 6, label: \"关键设施\", visible: false},\n        {key: 7, label: \"技术架构\", visible: false},\n        {key: 8, label: \"登录方式\", visible: false},\n        {key: 9, label: \"部署方式\", visible: false},\n        {key: 10, label: \"供应商\", visible: false},\n        {key: 11, label: \"服务对象\", visible: false},\n        {key: 12, label: \"授权用户数\", visible: false},\n        {key: 13, label: \"月均活跃人数\", visible: false},\n        {key: 14, label: \"上线状态\", visible: true},\n        {key: 15, label: \"审核状态\", visible: true},\n        {key: 16, label: \"备注\", visible: false},\n        {key: 17, label: \"标签\", visible: false},\n        {key: 18, label: \"严重漏洞\", visible: true},\n        {key: 19, label: \"高危漏洞\", visible: true},\n        {key: 20, label: \"中危漏洞\", visible: true},\n        {key: 21, label: \"IP地址\", visible: true},\n      ],\n      editItem: \"edit\",\n      editable: true,\n      step: 0,\n      currentComponent: 'AddApp',\n      auditApp: null,\n      remarkFrom: {\n        remarkMsg: '',\n      },\n      remarkDialog: false,\n      defaultShow: true,\n      applicationVersion: false,\n      applicationVisible: false,\n      optionColumns: [\"系统名称\", \"登录地址\", \"IP地址\",  \"所属部门\", \"责任人员\", \"联系电话\", \"等保级别\", \"关键设施\", \"技术架构\", \"登录方式\", \"部署方式\", \"供应商\", \"服务对象\", \"授权用户数\", \"月均活跃人数\", \"上线状态\", \"审核状态\", \"备注\", \"标签\", \"严重漏洞\",\"高危漏洞\",\"中危漏洞\"],\n      checkedColumns: [\"系统名称\", \"所属部门\", \"责任人员\", \"登录地址\",  \"IP地址\", \"等保级别\", \"上线状态\", \"审核状态\", \"严重漏洞\",\"高危漏洞\",\"中危漏洞\"],\n      tableKey: 1,\n    }\n  },\n  computed: {\n    ...mapGetters([\"activeNames\"]),\n  },\n  created() {\n    auditConfig({\n      pageNum: 1,\n      pageSize: 10\n    }).then(response => {\n      this.whetherOrNotToAudit = response.rows[0].configValue !== 'false';\n    })\n    this.initSearchDataAndListData();\n    // this.handleQuery();\n  },\n  watch: {\n    '$router.name'(val) {\n      if (val == 'Application') {\n        this.init();\n      } else {\n        this.open = false;\n      }\n    },\n    '$route.query': {\n      handler(val) {\n        if(val && val.deptId){\n          this.queryParams.deptId = parseInt(val.deptId);\n        }\n        if (val && val.domainId){\n          this.queryParams.domainId = val.domainId;\n        }\n        if (val && val.applicationIds){\n          this.queryParams.applicationIds = val.applicationIds;\n        }\n        if (val && val.systemType){\n          this.queryParams.systemType = val.systemType;\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  methods: {\n    // 导入功能\n    handleImport() {\n      this.upload.title = \"业务系统导入\";\n      this.upload.open = true;\n    },\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      if (response.code == 200)\n        this.$alert(\"成功导入\", \"导入结果\", {dangerouslyUseHTMLString: true});\n      else\n        this.$alert(response.msg, \"导入结果\", {dangerouslyUseHTMLString: true});\n      this.getList();\n      this.deptSelectKey += 1;\n    },\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    importTemplate() {\n      this.download('safe/application/importTemplateToJiangTong', {}, `application_${new Date().getTime()}.xlsx`)\n    },\n\n    // 初始化\n    async initSearchDataAndListData() {\n      await this.initSearchData();\n      this.init();\n    },\n\n    // 初始化查询条件\n    async initSearchData() {\n      for (let params of this.paramsArray) {\n        try {\n          const response = await getAppCountByDict(params);\n          if (params === 'system_type') this.systemsType = response.data.countByDictList || [];\n          if (params === 'asset_state') this.assetState = response.data.countByDictList || [];\n          if (params === 'protection_grade') this.protectionGrade = response.data.countByDictList || [];\n          if (params === 'appcheck_state') this.appcheckState = response.data.countByDictList || [];\n        } catch (error) {\n          console.error('请求失败:', error);\n        }\n      }\n    },\n\n    init() {\n      this.queryParams = Object.assign(this.queryParams, this.$route.params);\n      if (this.$route.params.add) {\n        this.$nextTick(() => {\n          this.handleAdd();\n          this.form.locationId = this.$route.params.locationId;\n          this.open = true;\n        })\n      }\n    },\n\n    //选中部门事件\n    deptSelect(node) {\n      this.queryParams.assetCode = null;\n      // this.queryParams.assetName = null;\n      if (node.id != null) {\n        this.queryParams.deptId = node.id;\n      }\n      this.handleQuery();\n    },\n\n    //排序\n    sortChange(column, prop, order) {\n      if (column.order != null) {\n        this.queryParams.isAsc = 'desc';\n      } else {\n        this.queryParams.isAsc = 'asc';\n      }\n      if (column.prop == 'state') {\n        this.queryParams.orderByColumn = \"e.state\";\n      } else\n        this.queryParams.orderByColumn = column.prop;\n      this.getList(this.queryParams);\n    },\n    /** 查询业务应用系统列表 */\n    getList() {\n\n      this.loading = true;\n      if(this.$route.params){\n        this.queryParams.ids = this.$route.params.ids;\n      }\n      listApplication(this.queryParams).then(response => {\n        this.applicationList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      }).finally(() => {\n        this.$nextTick(() => {\n          this.tableKey++;\n        })\n      })\n    },\n\n    // 表单重置\n    reset() {\n      this.editItem = \"edit\";\n      this.editable = true;\n      this.step = 0;\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.queryParams.systemType = null;\n      this.queryParams.state = null;\n      this.queryParams.protectGrade = null;\n      this.queryParams.checkOn = null;\n      this.queryParams.assetCode = null;\n      this.queryParams.assetName = null;\n      this.queryParams.url = null;\n      this.queryParams.domainUrl = null;\n      this.queryParams.domainId = null;\n      this.queryParams.applicationIds = [];\n      this.$refs.systemList1 && this.$refs.systemList1.resetSelection();\n      this.$refs.systemList2 && this.$refs.systemList2.resetSelection();\n      this.$refs.systemList3 && this.$refs.systemList3.resetSelection();\n      this.$refs.systemList4 && this.$refs.systemList4.resetSelection();\n      this.clearRouteQueryParams();\n      this.handleQuery();\n    },\n    clearRouteQueryParams(){\n      if(this.$route.params){\n        let queryParams = this.$route.params;\n        delete queryParams.ids;\n        this.$router.push({params: queryParams})\n      }\n    },\n\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.assetId);\n      this.assetNames = selection.map(item => item.assetName);\n      this.currentNames = selection.map(item => item.assetName);\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n\n    //提交审核\n    handleApply(app) {\n      this.auditApp = app\n      if (app.checkBy) {\n        this.remarkDialog = true\n      } else {\n        this.submitMsg()\n      }\n    },\n    formMsgSubmit() {\n      this.$refs['remarkFrom'].validate((valid) => {\n        if (valid) {\n          this.submitMsg()\n        }\n      });\n    },\n    submitMsg() {\n      applyApplication({\n        assetId: this.auditApp.assetId,\n        remark: this.remarkFrom.remarkMsg\n      }).then(res => {\n        this.$modal.msgSuccess(\"已经提交审核！\");\n        this.getList();\n        this.callOffMsg()\n      })\n    },\n    callOffMsg() {\n      this.auditApp = null\n      this.remarkFrom.remarkMsg = ''\n      this.remarkDialog = false\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.title = '添加应用信息';\n      this.params = {};\n      this.applicationVisible = true;\n      // this.$tab.openPage(\n      //   \"添加应用信息\",\n      //   \"/asset-ledger/monitor2/application/info\",\n      //   {}\n      // );\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row, edit = true, data) {\n      this.reset();\n      if (row.checkOn === \"pass\" && (data === undefined || data === null)) {\n        this.title = '修改应用信息'\n        this.editable = edit\n        const assetId = row.assetId || this.ids\n        this.params = {assetId, ...data};\n        this.params.isEdit = true;\n        this.applicationVisible = true;\n        // this.$tab.openPage(title, '/asset-ledger/monitor2/application/info', params);\n      } else {\n        this.title = data ? data.showData === 'false' ? '查看应用信息' : '修改应用信息' : '修改应用信息';\n        this.editable = edit;\n        const assetId = row.assetId || this.ids;\n        this.params = {assetId, ...data};\n        this.applicationVisible = true;\n        // this.$tab.openPage(title, '/asset-ledger/monitor2/application/info', {assetId, ...data});\n      }\n    },\n\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const assetIds = row.assetId || this.ids;\n      let assetsName = \"\";\n      if (!row.assetId) {\n        assetsName = this.currentNames.join(\",\");\n      } else {\n        assetsName = row.assetName;\n      }\n\n      this.$modal.confirm('是否确认删除【' + assetsName + '】的数据项？').then(function () {\n        return delApplication(assetIds);\n      }).then(() => {\n        this.getList();\n        this.deptSelectKey += 1;\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('safe/application/export', {\n        ...this.queryParams\n      }, `application_${new Date().getTime()}.xlsx`)\n    },\n    applicationChange(data){\n      this.applicationVisible = data;\n      this.getList();\n      this.initSearchData();\n    },\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../../assets/styles/assetIndex.scss\";\n.small-padding {\n  padding-left: 0;\n  padding-right: 0;\n  width: 150px;\n}\n\n.operate {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 30px;\n}\n\n.option-app {\n  margin-right: 10px;\n}\n\n.r_popover {\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  width: 150px;\n  overflow: hidden;\n}\n\n</style>\n"]}]}