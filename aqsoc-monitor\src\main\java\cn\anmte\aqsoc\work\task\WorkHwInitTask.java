package cn.anmte.aqsoc.work.task;

import cn.anmte.aqsoc.work.domain.WorkHwTask;
import cn.anmte.aqsoc.work.service.IWorkHwTaskService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.5.3_dev
 * @description:
 * @date 2025-08-27 10:34
 */
@Component
@Slf4j
public class WorkHwInitTask {
    @Resource
    private IWorkHwTaskService workHwTaskService;

    @PostConstruct
    public void init(){
        ThreadUtil.execute(() -> {
            ThreadUtil.sleep(3000); //延迟3秒启动
            startDelayTask();
        });
    }

    public void startDelayTask() {
        DateTime nowDate = DateUtil.date();
        WorkHwTask query = new WorkHwTask();
        query.setStartTime(nowDate);
        List<WorkHwTask> list = workHwTaskService.selectWorkHwTaskList(query);
        if(CollUtil.isNotEmpty(list)){
            log.info("启动HW定时任务，数量:" + list.size());
            list.forEach(workHwTask -> workHwTaskService.startDelayTask(workHwTask));
        }
    }
}
