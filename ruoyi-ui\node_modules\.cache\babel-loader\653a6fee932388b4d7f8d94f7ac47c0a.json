{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\index.vue", "mtime": 1756294198396}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_form", "_interopRequireDefault", "require", "_bigForm", "_vuex", "_crud", "_plan", "components", "Form", "BigForm", "Plan", "props", "data", "keyword", "expandObj", "query", "list", "listLoading", "formVisible", "formVisible1", "total", "mergeList", "list<PERSON>uery", "currentPage", "pageSize", "makePlanShow", "curRow", "computed", "_objectSpread2", "default", "mapGetters", "menuId", "$route", "meta", "modelId", "created", "initSearchDataAndListData", "methods", "addOrUpdateHandle", "id", "isDetail", "is<PERSON><PERSON><PERSON>", "isCopy", "_this", "$nextTick", "$refs", "addForm", "init", "updateHandle", "row", "_this2", "makePlan", "copy", "arraySpanMethod", "_ref", "column", "i", "length", "property", "prop", "rowspan", "colspan", "sortChange", "_ref2", "order", "initData", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "initSearchData", "stop", "_callee2", "_callee2$", "_context2", "_this4", "_query", "getList", "then", "res", "_list", "_data", "push", "map", "o", "pagination", "search", "sort", "sidx", "refresh", "isrRefresh", "reset", "key", "undefined", "handleDel", "_this5", "$confirm", "type", "delData", "$message", "message", "msg", "catch"], "sources": ["src/views/aqsoc/hw-work/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"custom-container\">\r\n    <div v-show=\"!formVisible1 && !makePlanShow\" class=\"custom-content-container-right\">\r\n      <div class=\"custom-content-search-box\">\r\n        <el-row :gutter=\"16\">\r\n          <el-form @submit.native.prevent>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"年度\">\r\n                <el-input v-model=\"query.year\" placeholder=\"请输入年度\" clearable>\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"技术支撑单位\">\r\n                <el-input v-model=\"query.supportOrgs\" placeholder=\"请输入技术支撑单位\" clearable>\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item class=\"custom-search-btn\">\r\n                <el-button class=\"btn1\" size=\"small\" @click=\"search()\">查询</el-button>\r\n                <el-button class=\"btn2\" size=\"small\" @click=\"reset()\">重置</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-form>\r\n        </el-row>\r\n      </div>\r\n\r\n      <div class=\"custom-content-container\">\r\n        <div class=\"common-header\">\r\n          <div><span class=\"common-head-title\">HW事务列表</span></div>\r\n          <div class=\"common-head-right\">\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"small\"\r\n                  @click=\"addOrUpdateHandle()\"\r\n                >新增\r\n                </el-button >\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </div>\r\n        <el-table height=\"100%\" v-loading=\"listLoading\" :data=\"list\" @sort-change='sortChange'\r\n                  :span-method=\"arraySpanMethod\">\r\n          <el-table-column\r\n            prop=\"year\"\r\n            label=\"年度\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"supportOrgs\"\r\n            label=\"技术支撑单位\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"userNames\"\r\n            label=\"联络人\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"hwStart\"\r\n            label=\"HW开始时间\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"hwEnd\"\r\n            label=\"HW结束时间\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"createTime\"\r\n            label=\"创建时间\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" @click=\"makePlan(scope.row)\">制定HW计划</el-button>\r\n              <el-button type=\"text\" @click=\"updateHandle(scope.row, true)\">HW看板</el-button>\r\n              <el-button type=\"text\" @click=\"copy(scope.row)\">复制</el-button>\r\n              <el-button type=\"text\" class=\"table-delBtn\" @click=\"handleDel(scope.row.id)\">删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination :total=\"total\" :page.sync=\"listQuery.currentPage\" :limit.sync=\"listQuery.pageSize\"\r\n                    @pagination=\"initData\"></pagination>\r\n      </div>\r\n      <Form :visible.sync=\"formVisible\" ref=\"addForm\" @refresh=\"refresh\"></Form>\r\n    </div>\r\n    <big-form :visible.sync=\"formVisible1\" ref=\"BigForm\" @refresh=\"refresh\" ></big-form>\r\n    <plan v-if=\"makePlanShow\" :show.sync=\"makePlanShow\" :work-info=\"curRow\"></plan>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Form from './form'\r\nimport BigForm from './big-form'\r\nimport {mapGetters} from \"vuex\";\r\nimport {getList, delData} from '@/api/aqsoc/work-hw/crud'\r\nimport Plan from './plan'\r\n\r\nexport default {\r\n  components: {Form, BigForm,Plan},\r\n  props: {},\r\n  data() {\r\n    return {\r\n      keyword: '',\r\n      expandObj: {},\r\n      query: {},\r\n      list: [],\r\n      listLoading: true,\r\n      formVisible: false,\r\n      formVisible1: false,\r\n      total: 0,\r\n      mergeList: [],\r\n      listQuery: {\r\n        currentPage: 1,\r\n        pageSize: 20\r\n      },\r\n      makePlanShow: false,\r\n      curRow: null,\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['userInfo']),\r\n    menuId() {\r\n      return this.$route.meta.modelId || ''\r\n    }\r\n  },\r\n  created() {\r\n    this.initSearchDataAndListData()\r\n  },\r\n  methods: {\r\n    addOrUpdateHandle(id, isDetail, isAudit, isCopy) {\r\n      this.formVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.addForm.init(id, isDetail, isAudit,isCopy)\r\n      })\r\n    },\r\n    updateHandle(row, isDetail, isAudit) {\r\n      this.formVisible1 = true\r\n      this.$nextTick(() => {\r\n        this.$refs.BigForm.init(row, isDetail, isAudit)\r\n      })\r\n    },\r\n    makePlan(row){\r\n      this.curRow = row;\r\n      this.makePlanShow = true;\r\n    },\r\n    copy(row){\r\n      this.addOrUpdateHandle(row.id,true,true,true);\r\n    },\r\n    arraySpanMethod({column}) {\r\n      for (let i = 0; i < this.mergeList.length; i++) {\r\n        if (column.property == this.mergeList[i].prop) {\r\n          return [this.mergeList[i].rowspan, this.mergeList[i].colspan]\r\n        }\r\n      }\r\n    },\r\n    sortChange({column, prop, order}) {\r\n      this.initData()\r\n    },\r\n    async initSearchDataAndListData() {\r\n      await this.initSearchData()\r\n      this.initData()\r\n    },\r\n    //初始化查询的默认数据\r\n    async initSearchData() {\r\n    },\r\n    initData() {\r\n      this.listLoading = true;\r\n      let _query = {\r\n        ...this.listQuery,\r\n        ...this.query,\r\n        keyword: this.keyword,\r\n        menuId: this.menuId\r\n      };\r\n      getList(_query).then(res => {\r\n        var _list = [];\r\n        for (let i = 0; i < res.data.list.length; i++) {\r\n          let _data = res.data.list[i];\r\n          _list.push(_data)\r\n        }\r\n        this.list = _list.map(o => ({\r\n          ...o,\r\n          ...this.expandObj,\r\n        }))\r\n        this.total = res.data.pagination.total\r\n        this.listLoading = false\r\n      })\r\n    },\r\n    search() {\r\n      this.listQuery.currentPage = 1\r\n      this.listQuery.pageSize = 20\r\n      this.listQuery.sort = \"desc\"\r\n      this.listQuery.sidx = \"\"\r\n      this.initData()\r\n    },\r\n    refresh(isrRefresh) {\r\n      this.formVisible = false\r\n      this.formVisible1 = false\r\n      if (isrRefresh) this.reset()\r\n    },\r\n    reset() {\r\n      for (let key in this.query) {\r\n        this.query[key] = undefined\r\n      }\r\n      this.search()\r\n    },\r\n    handleDel(id) {\r\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delData(id).then(res => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: res.msg,\r\n          });\r\n          this.initData()\r\n        })\r\n      }).catch(() => {\r\n      });\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA2FA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAL,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,UAAA;IAAAC,IAAA,EAAAA,aAAA;IAAAC,OAAA,EAAAA,gBAAA;IAAAC,IAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,KAAA;MACAC,IAAA;MACAC,WAAA;MACAC,WAAA;MACAC,YAAA;MACAC,KAAA;MACAC,SAAA;MACAC,SAAA;QACAC,WAAA;QACAC,QAAA;MACA;MACAC,YAAA;MACAC,MAAA;IACA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA;IACAC,MAAA,WAAAA,OAAA;MACA,YAAAC,MAAA,CAAAC,IAAA,CAAAC,OAAA;IACA;EAAA,EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,yBAAA;EACA;EACAC,OAAA;IACAC,iBAAA,WAAAA,kBAAAC,EAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,MAAA;MAAA,IAAAC,KAAA;MACA,KAAAzB,WAAA;MACA,KAAA0B,SAAA;QACAD,KAAA,CAAAE,KAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAR,EAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,MAAA;MACA;IACA;IACAM,YAAA,WAAAA,aAAAC,GAAA,EAAAT,QAAA,EAAAC,OAAA;MAAA,IAAAS,MAAA;MACA,KAAA/B,YAAA;MACA,KAAAyB,SAAA;QACAM,MAAA,CAAAL,KAAA,CAAApC,OAAA,CAAAsC,IAAA,CAAAE,GAAA,EAAAT,QAAA,EAAAC,OAAA;MACA;IACA;IACAU,QAAA,WAAAA,SAAAF,GAAA;MACA,KAAAvB,MAAA,GAAAuB,GAAA;MACA,KAAAxB,YAAA;IACA;IACA2B,IAAA,WAAAA,KAAAH,GAAA;MACA,KAAAX,iBAAA,CAAAW,GAAA,CAAAV,EAAA;IACA;IACAc,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,QAAAnC,SAAA,CAAAoC,MAAA,EAAAD,CAAA;QACA,IAAAD,MAAA,CAAAG,QAAA,SAAArC,SAAA,CAAAmC,CAAA,EAAAG,IAAA;UACA,aAAAtC,SAAA,CAAAmC,CAAA,EAAAI,OAAA,OAAAvC,SAAA,CAAAmC,CAAA,EAAAK,OAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,KAAA;MAAA,IAAAR,MAAA,GAAAQ,KAAA,CAAAR,MAAA;QAAAI,IAAA,GAAAI,KAAA,CAAAJ,IAAA;QAAAK,KAAA,GAAAD,KAAA,CAAAC,KAAA;MACA,KAAAC,QAAA;IACA;IACA7B,yBAAA,WAAAA,0BAAA;MAAA,IAAA8B,MAAA;MAAA,WAAAC,kBAAA,CAAAtC,OAAA,mBAAAuC,oBAAA,CAAAvC,OAAA,IAAAwC,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAvC,OAAA,IAAA0C,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAT,MAAA,CAAAU,cAAA;YAAA;cACAV,MAAA,CAAAD,QAAA;YAAA;YAAA;cAAA,OAAAQ,QAAA,CAAAI,IAAA;UAAA;QAAA,GAAAP,OAAA;MAAA;IACA;IACA;IACAM,cAAA,WAAAA,eAAA;MAAA,WAAAT,kBAAA,CAAAtC,OAAA,mBAAAuC,oBAAA,CAAAvC,OAAA,IAAAwC,IAAA,UAAAS,SAAA;QAAA,WAAAV,oBAAA,CAAAvC,OAAA,IAAA0C,IAAA,UAAAQ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAN,IAAA,GAAAM,SAAA,CAAAL,IAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAH,IAAA;UAAA;QAAA,GAAAC,QAAA;MAAA;IACA;IACAb,QAAA,WAAAA,SAAA;MAAA,IAAAgB,MAAA;MACA,KAAAhE,WAAA;MACA,IAAAiE,MAAA,OAAAtD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,KAAAP,SAAA,GACA,KAAAP,KAAA;QACAF,OAAA,OAAAA,OAAA;QACAkB,MAAA,OAAAA;MAAA,EACA;MACA,IAAAoD,aAAA,EAAAD,MAAA,EAAAE,IAAA,WAAAC,GAAA;QACA,IAAAC,KAAA;QACA,SAAA9B,CAAA,MAAAA,CAAA,GAAA6B,GAAA,CAAAzE,IAAA,CAAAI,IAAA,CAAAyC,MAAA,EAAAD,CAAA;UACA,IAAA+B,KAAA,GAAAF,GAAA,CAAAzE,IAAA,CAAAI,IAAA,CAAAwC,CAAA;UACA8B,KAAA,CAAAE,IAAA,CAAAD,KAAA;QACA;QACAN,MAAA,CAAAjE,IAAA,GAAAsE,KAAA,CAAAG,GAAA,WAAAC,CAAA;UAAA,WAAA9D,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA6D,CAAA,GACAT,MAAA,CAAAnE,SAAA;QAAA,CACA;QACAmE,MAAA,CAAA7D,KAAA,GAAAiE,GAAA,CAAAzE,IAAA,CAAA+E,UAAA,CAAAvE,KAAA;QACA6D,MAAA,CAAAhE,WAAA;MACA;IACA;IACA2E,MAAA,WAAAA,OAAA;MACA,KAAAtE,SAAA,CAAAC,WAAA;MACA,KAAAD,SAAA,CAAAE,QAAA;MACA,KAAAF,SAAA,CAAAuE,IAAA;MACA,KAAAvE,SAAA,CAAAwE,IAAA;MACA,KAAA7B,QAAA;IACA;IACA8B,OAAA,WAAAA,QAAAC,UAAA;MACA,KAAA9E,WAAA;MACA,KAAAC,YAAA;MACA,IAAA6E,UAAA,OAAAC,KAAA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,SAAAC,GAAA,SAAAnF,KAAA;QACA,KAAAA,KAAA,CAAAmF,GAAA,IAAAC,SAAA;MACA;MACA,KAAAP,MAAA;IACA;IACAQ,SAAA,WAAAA,UAAA7D,EAAA;MAAA,IAAA8D,MAAA;MACA,KAAAC,QAAA;QACAC,IAAA;MACA,GAAAnB,IAAA;QACA,IAAAoB,aAAA,EAAAjE,EAAA,EAAA6C,IAAA,WAAAC,GAAA;UACAgB,MAAA,CAAAI,QAAA;YACAF,IAAA;YACAG,OAAA,EAAArB,GAAA,CAAAsB;UACA;UACAN,MAAA,CAAApC,QAAA;QACA;MACA,GAAA2C,KAAA,cACA;IACA;EACA;AACA", "ignoreList": []}]}