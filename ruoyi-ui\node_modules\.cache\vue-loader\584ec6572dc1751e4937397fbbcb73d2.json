{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\alertEvent.vue?vue&type=template&id=13dea1c2&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\alertEvent.vue", "mtime": 1756294198424}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}]}