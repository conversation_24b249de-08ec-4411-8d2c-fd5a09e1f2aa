package cn.anmte.aqsoc.work.model;

import cn.anmte.aqsoc.common.SystemUtilService;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.ruoyi.common.core.domain.entity.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fumixuan.gencode.common.dict.DictField;

/**
 * <p>
 * 护网事务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Getter
@Setter
@Schema(name = "WorkHwListVO", description = "护网事务表")
public class WorkHwListVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "自增id")
    private Integer id;

    @Schema(description = "事务模版id")
    private Integer templateId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "年度")
    private String year;

    @Schema(description = "责任人")
    private String userId;

    private String userNames;

    @Schema(description = "HW开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime hwStart;

    @Schema(description = "HW结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime hwEnd;

    @Schema(description = "技术支撑单位")
    private String supportOrgs;

    @Schema(description = "参与人员")
    private String supportUsers;

    @Schema(description = "护网数据json")
    private String dataJson;

    @Schema(description = "所属单位")
    private Long deptId;

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime createTime;

    List<SystemUtilService.DeptUsers> usersGroupByDept;


}
