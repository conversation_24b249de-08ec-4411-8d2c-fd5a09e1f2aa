package com.ruoyi.ffsafe.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 主机入侵攻击事件对象 ffsafe_host_intrusion_attack
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FfsafeHostIntrusionAttack extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 非凡返回的ID */
    @Excel(name = "事件ID", sort = 1)
    private Integer ffId;

    /** 攻击源IP */
    @Excel(name = "攻击源IP", sort = 2)
    private String sip;

    /** 目标IP */
    @Excel(name = "目标IP", sort = 3)
    private String dip;

    /** 目标IP主机名 */
    @Excel(name = "目标主机名", sort = 4)
    private String dipName;

    /** 告警名称 */
    @Excel(name = "告警名称", sort = 5)
    private String alertName;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 6)
    private Date startTime;

    /** 最近告警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最近告警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 7)
    private Date updateTime;

    /** 告警详情 */
    @Excel(name = "告警详情", sort = 8)
    private String alertDetail;

    /** 处置状态: 0=未处置,1=已处置,2=忽略 */
    @Excel(name = "处置状态", readConverterExp = "0=未处置,1=已处置,2=忽略", sort = 9)
    private Integer handleState;

    /** 处置描述 */
    @Excel(name = "处置描述", sort = 10)
    private String handleDesc;

    /** 处置人 */
    @Excel(name = "处置人", sort = 11)
    private String disposer;

    /** 设备配置ID */
    private Long deviceConfigId;

    /**
     * 获取处置状态文本
     */
    public String getHandleStateText() {
        if (handleState == null) {
            return "未处置";
        }
        switch (handleState) {
            case 0:
                return "未处置";
            case 1:
                return "已处置";
            case 2:
                return "忽略";
            default:
                return "未知";
        }
    }

    /**
     * 设置处置状态
     */
    public void setHandleStateByText(String handleStateText) {
        if ("未处置".equals(handleStateText)) {
            this.handleState = 0;
        } else if ("已处置".equals(handleStateText)) {
            this.handleState = 1;
        } else if ("忽略".equals(handleStateText)) {
            this.handleState = 2;
        } else {
            this.handleState = 0; // 默认未处置
        }
    }
}
