{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\system\\user\\index.vue?vue&type=template&id=1930a3c4&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\system\\user\\index.vue", "mtime": 1756294198427}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}