<template>
  <div class="alert-event-box">
    <div class="head-card-box">
      <div class="head-card-item" v-for="(headItem,index) in headCardOptions" :key="'head-'+index">
        <div :class="currentCard === headItem.key ? 'head-card active' : 'head-card'" @click="headItem.click">
          <div class="head-card-title">
            {{ headItem.title }}
            <span style="margin-left: 5px">
              <el-tag type="primary" effect="dark" size="mini">{{ headItem.total() }}</el-tag>
            </span>
          </div>
          <div class="head-card-btn-box">
            <el-row :gutter="20" style="height: 100%;display: flex;align-items: center;margin-left: 0;margin-right: 0">
              <el-col
                v-for="(btnItem,btnIndex) in headItem.btnArr"
                :key="'btn-'+btnIndex"
                :span="24/headItem.btnArr.length"
                :class="currentCard === headItem.key && currentBtn === btnItem.key ? 'head-btn-active' : ''"
                style="padding-top: 10px;padding-bottom: 10px;"
              >
                <div class="head-card-btn" @click.stop="btnItem.click">
                  <div :class="[ btnItem.label === '弱口令账号' ? 'btn-icon1' : 'btn-icon']">
                    <el-image :src="btnItem.icon" />
                  </div>
                  <div class="btn-content">
                    <div class="btn-content-value">
                      {{ btnItem.value() }}
                    </div>
                    <div class="btn-content-label">
                      {{ btnItem.label }}
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
    <div v-if="currentCard === 1" class="box-content">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="告警列表" name="first" />
        <el-tab-pane label="攻击者视角" name="second" />
        <el-tab-pane label="受害者视角" name="third" />
        <el-tab-pane label="资产视角" name="four" />
        <!--        <el-tab-pane label="阻断IP" name="five"></el-tab-pane>-->
      </el-tabs>
      <div style="height: calc(100% - 41px); margin-top: 3px">
        <event-list v-if="propActiveName === 'first'" :props-active-name="propActiveName" :props-query-params="queryParams" :current-btn.sync="currentBtn" @getList="handleGetEventList" />
        <attack-view-list v-if="propActiveName === 'second'" :props-active-name="propActiveName" :props-query-params="queryParams" :current-btn.sync="currentBtn" @getList="handleGetAttackEventList" />
        <suffer-view-list v-if="propActiveName === 'third'" :props-active-name="propActiveName" :props-query-params="queryParams" :current-btn.sync="currentBtn" @getList="handleGetEventList" />
        <asset-view v-if="propActiveName === 'four'" :props-active-name="propActiveName" :props-query-params="queryParams" :current-btn.sync="currentBtn" @getList="handleGetEventList" />
      </div>
    </div>
    <div v-if="currentCard === 2" class="box-content">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="告警列表" name="first" />
        <el-tab-pane label="攻击者视角" name="second" />
      </el-tabs>
      <div style="height: calc(100% - 41px); margin-top: 3px">
        <honeypotAlarmList v-if="propActiveName === 'first'" :props-active-name="propActiveName" :props-query-params="queryParams" :current-btn.sync="currentBtn" @getList="handleGetHoneyEventList" />
        <honeypot-attack-view-list v-if="propActiveName === 'second'" :props-active-name="propActiveName" :props-query-params="queryParams" :current-btn.sync="currentBtn" @getList="handleGetHoneyEventList" />
      </div>
    </div>
    <div v-if="currentCard === 3" class="box-content">
      <api-alarm-list v-if="currentCard === 3" :props-active-name="'apiAlarm'" :props-query-params="queryParams" @reset-button="handleResetButton" @query-change="handleApiQueryChange" @getList="handleGetApiEventList" />
    </div>
    <div v-if="currentCard === 4" class="box-content">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="阻断中列表" name="first" />
        <el-tab-pane label="阻断历史记录" name="second" />
      </el-tabs>
      <div style="height: calc(100% - 47px); margin-top: 8px">
        <IpfilterLog v-if="propActiveName === 'first'" ref="ipFilterLog" :props-active-name="propActiveName" @getList="handleGetIpFilterList" />
        <IpFilterLogHistory v-if="propActiveName === 'second'" ref="ipFilterLogHistory" :props-active-name="propActiveName" @getList="handleGetIpFilterList" />
      </div>
    </div>
    <div v-if="currentCard === 5" class="box-content">
      <invade-attack ref="invadeAttack" v-if="propActiveName === 'first'"/>
      <host-event ref="hostEvent" v-if="propActiveName === 'second'"/>
    </div>
  </div>
</template>

<script>
import EventList from './component/eventList'
import AttackViewList from './component/attackViewList'
import SufferViewList from './component/sufferViewList'
import AssetView from '@/views/threat/asset/index'
import IpfilterLog from '@/views/aqsoc/ffsafe-ipfilter-log/index'
import IpFilterLogHistory from '@/views/aqsoc/ffsafe-ipfilter-log/history'
import { groupAlarmLevelStatistics, groupHoneypotAlarmLevelStatistics } from '@/api/threat/threat'
import { groupAlarmLevelStatistics as groupAttackAlarmLevelStatistics } from '@/api/threaten/AttackAlarm';
import HoneypotAlarmList from './component/honeypotAlarmList'
import HoneypotAttackViewList from './component/honeypotAttackViewList'
import ApiAlarmList from './component/apiAlarmList'
import HostEvent from './component/hostEvent.vue'
import InvadeAttack from './component/invadeAttack.vue'
import { getFilterLogStatistic } from '@/api/safe/ffsafeIpFilterblocking'
import { getFlowRiskAssetsStatistics } from '@/api/ffsafe/flowRiskAssets'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'AlertEvent',
  components: {
    SufferViewList,
    AttackViewList,
    EventList,
    AssetView,
    IpfilterLog,
    IpFilterLogHistory,
    HoneypotAlarmList,
    HoneypotAttackViewList,
    ApiAlarmList,
    HostEvent,
    InvadeAttack
  },
  data() {
    return {
      activeName: 'first',
      propActiveName: 'first',
      srcQueryParams: {},
      queryParams: {},
      currentQueryParams: {},
      currentCard: 1,
      currentBtn: null,
      headCardOptions: [
        {
          title: '流量威胁告警',
          total: () => this.getStatisticsValue(this.getGroupStatisticsData, 'total'),
          key: 1,
          click: () => {
            this.headBtnClick(1, null)
            this.currentQueryParams.alarmLevel = null;
            this.currentQueryParams.riskLevel = null;
            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
          },
          btnArr: [
            /*{
              icon: require('@/assets/icons/event/level5.png'),
              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel5'),
              label: '严重',
              key: 5,
              click: () => {
                this.headBtnClick(1, 5)
                this.currentQueryParams.alarmLevel = '5'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            },*/
            {
              icon: require('@/assets/icons/event/level4.png'),
              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel4'),
              label: '高危',
              key: 4,
              click: () => {
                this.headBtnClick(1, 4)
                this.currentQueryParams.alarmLevel = '4'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            },
            {
              icon: require('@/assets/icons/event/level3.png'),
              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel3'),
              label: '中危',
              key: 3,
              click: () => {
                this.headBtnClick(1, 3)
                this.currentQueryParams.alarmLevel = '3'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            },
            {
              icon: require('@/assets/icons/event/level2.png'),
              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel2'),
              label: '低危',
              key: 2,
              click: () => {
                this.headBtnClick(1, 2)
                this.currentQueryParams.alarmLevel = '2'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            }
          ]
        },
        {
          title: '蜜罐诱捕告警',
          total: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'total'),
          key: 2,
          click: () => this.headBtnClick(2, null),
          btnArr: [
            {
              icon: require('@/assets/icons/event/level5.png'),
              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel5'),
              label: '严重',
              key: 5,
              click: () => {
                this.headBtnClick(2, 5)
                this.currentQueryParams.alarmLevel = '5'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            },
            {
              icon: require('@/assets/icons/event/level4.png'),
              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel4'),
              label: '高危',
              key: 4,
              click: () => {
                this.headBtnClick(2, 4)
                this.currentQueryParams.alarmLevel = '4'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            },
            {
              icon: require('@/assets/icons/event/level3.png'),
              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel3'),
              label: '中危',
              key: 3,
              click: () => {
                this.headBtnClick(2, 3)
                this.currentQueryParams.alarmLevel = '3'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            },
            {
              icon: require('@/assets/icons/event/level2.png'),
              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel2'),
              label: '低危',
              key: 2,
              click: () => {
                this.headBtnClick(2, 2)
                this.currentQueryParams.alarmLevel = '2'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            }
          ]
        },
        {
          title: 'API告警',
          total: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'total'),
          key: 3,
          click: () => {
            this.headBtnClick(3, null)
            this.currentQueryParams.riskType = null
            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
          },
          btnArr: [
            {
              icon: require('@/assets/icons/event/weakPassword.png'),
              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'weakPassword'),
              label: '弱口令账号',
              key: 1,
              click: () => {
                this.headBtnClick(3, 1)
                this.currentQueryParams.riskType = 'weak_password'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            },
            {
              icon: require('@/assets/icons/event/敏感信息.png'),
              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'sensitiveInfo'),
              label: '敏感信息',
              key: 2,
              click: () => {
                this.headBtnClick(3, 2)
                this.currentQueryParams.riskType = 'sensitive_info'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            },
            {
              icon: require('@/assets/icons/event/高危资产.png'),
              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'highRiskAssets'),
              label: '高危资产',
              key: 3,
              click: () => {
                this.headBtnClick(3, 3)
                this.currentQueryParams.riskType = 'high_risk_assets'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            }
          ]
        },
        {
          title: '主机Agent事件',
          total: () => this.getStatisticsValue(this.hostAgentEventStatisticsData, 'total'),
          key: 5,
          click: () => {
            this.headBtnClick(5, null)
            // this.currentQueryParams.riskType = 'host_agent_event'
            this.currentQueryParams.riskType = null
            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
          },
          btnArr: [
            {
              icon: require('@/assets/icons/event/invade.png'),
              value: () => this.getStatisticsValue(this.hostAgentEventStatisticsData, 'invadeAttack'),
              label: '入侵攻击',
              key: 1,
              click: () => {
                this.headBtnClick(5, 1)
                this.propActiveName = 'first'
                // this.currentQueryParams.riskType = 'invade'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            },
            {
              icon: require('@/assets/icons/event/host-event.png'),
              value: () => this.getStatisticsValue(this.hostAgentEventStatisticsData, 'hostEvent'),
              label: '主机事件',
              key: 2,
              click: () => {
                this.headBtnClick(5, 2)
                this.propActiveName = 'second'
                // this.currentQueryParams.riskType = 'malware'
                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
              }
            }
          ]
        },
        {
          title: '实时阻断',
          total: () => this.getStatisticsValue(this.filterLogStatisticData, 'total'),
          key: 4,
          click: () => {
            this.headBtnClick(4, null)
            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
          },
          btnArr: [
            {
              icon: require('@/assets/icons/event/正在阻断.png'),
              value: () => this.getStatisticsValue(this.filterLogStatisticData, 'blockingCount'),
              label: '正在阻断',
              key: 1,
              click: () => {
                this.propActiveName = 'first'
                this.activeName = 'first'
                this.headBtnClick(4, 1)
              }
            },
            {
              icon: require('@/assets/icons/event/阻断历史.png'),
              value: () => this.getStatisticsValue(this.filterLogStatisticData, 'blockLogCount'),
              label: '历史记录',
              key: 2,
              click: () => {
                this.propActiveName = 'second'
                this.activeName = 'second'
                this.$forceUpdate()
                this.headBtnClick(4, 2)
              }
            }
          ]
        }
      ],
      threatenAlarmStatisticsData: {},
      attackAlarmStatisticsData: {},
      honeypotAlarmStatisticsData: {},
      filterLogStatisticData: {},
      apiAlarmStatisticsData: {},
      hostAgentEventStatisticsData: {},
    }
  },
  watch: {
    $route: {
      handler(newVal) {
        if (newVal.query.type === '4') {
          // 设置当前选中卡片和按钮
          this.currentCard = 4 // 对应实时阻断卡片
          this.currentBtn = 1 // 对应正在阻断按钮

          // 如果需要触发按钮点击逻辑
          this.$nextTick(() => {
            // 调用按钮点击方法
            this.headCardOptions[3].btnArr[0].click()
          })
        }
        if (newVal.query.type === '2') {
          this.currentCard = 2
          this.currentBtn = 2
        }
      },
      immediate: true
    },
    currentCard: {
      handler(newVal) {
        // 当切换到API告警卡片时，刷新数据
        // 注释掉这个调用，避免重复调用统计接口，统计数据由子组件的getList事件触发
        // if (newVal === 3) {
        //   this.refreshApiAlarmStatistics()
        // }
      }
    },
    activeName: {
      handler(newVal) {
        if (this.currentCard === 4) {
          if (newVal === 'first') {
            this.currentBtn = 1
          }
          if (newVal === 'second') {
            this.currentBtn = 2
          }
        }
      }
    }
  },
  mounted() {
    const query = this.$route.query
    if (query) {
      this.srcQueryParams = query
      this.queryParams = { ...this.srcQueryParams }
      if (query.tabs) {
        this.propActiveName = query.tabs
        this.activeName = query.tabs
      }
    }
    const params = {
      startTime: parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'),
      endTime: parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'),
      handleState: '0'
    }
    this.getAlarmLevelStatistics(params)
    this.getHoneypotAlarmLevelStatistics(params)
    this.getFilterLogStatistic({})
    this.getFlowRiskAssetsStatistics({
      params: {
        beginTime: params.startTime,
        endTime: params.endTime
      },
      handleState: params.handleState
    })
  },
  computed: {
    getGroupStatisticsData() {
      return this.activeName === 'second' ? this.attackAlarmStatisticsData : this.threatenAlarmStatisticsData;
    }
  },
  methods: {
    getAlarmLevelStatistics(params) {
      groupAlarmLevelStatistics(params).then(res => {
        this.threatenAlarmStatisticsData = res.data
      })
    },
    getAttackAlarmLevelStatistics(params) {
      groupAttackAlarmLevelStatistics(params).then(res => {
        this.attackAlarmStatisticsData = res.data
      })
    },
    getHoneypotAlarmLevelStatistics(params) {
      groupHoneypotAlarmLevelStatistics(params).then(res => {
        this.honeypotAlarmStatisticsData = res.data
      })
    },
    getFilterLogStatistic(params) {
      getFilterLogStatistic(params).then(res => {
        this.filterLogStatisticData = res.data
      })
    },
    getFlowRiskAssetsStatistics(params) {
      getFlowRiskAssetsStatistics(params).then(res => {
        this.apiAlarmStatisticsData = res.data
      })
    },
    handleClick() {
      this.propActiveName = this.activeName
      this.$router.push({ query: {}})
    },
    headBtnClick(cardKey, btnKey) {
      if (this.currentCard !== cardKey) {
        this.currentBtn = null
      }
      this.currentQueryParams = {}

      // 根据卡片类型设置对应的默认标签页
      if (cardKey === 1) {
        // 流量威胁告警：重置为告警列表标签页
        if (this.currentCard !== cardKey) {
          this.activeName = 'first'
          this.propActiveName = 'first'
        }
      } else if (cardKey === 2) {
        // 蜜罐诱捕告警：重置为告警列表标签页
        if (this.currentCard !== cardKey) {
          this.activeName = 'first'
          this.propActiveName = 'first'
        }
      }
      this.currentCard = cardKey
      this.currentBtn = btnKey
      // cardKey === 3(API告警) 不需要设置标签页，因为没有子标签页
      // currentCard === 4 (实时阻断) 不需要设置标签页，因为没有子标签页
    },
    getStatisticsValue(srcData, key) {
      if (!srcData) {
        return 0
      }
      return srcData[key] || 0
    },
    handleResetButton() {
      // 重置API告警按钮选中状态
      if (this.currentCard === 3) {
        this.currentBtn = null
        // 重置查询参数中的风险类型
        this.currentQueryParams.riskType = null
        this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }
      }
    },
    handleApiQueryChange(queryChange) {
      // 处理API告警查询参数变化，同步按钮选中状态
      if (queryChange.riskType) {
        // 根据风险类型设置对应的按钮选中状态
        switch (queryChange.riskType) {
          case 'weak_password':
            this.currentBtn = 1
            break
          case 'sensitive_info':
            this.currentBtn = 2
            break
          case 'high_risk_assets':
            this.currentBtn = 3
            break
          default:
            this.currentBtn = null
        }
      } else {
        this.currentBtn = null
      }
    },
    handleGetEventList(params) {
      if (!params.alarmLevel) {
        this.currentBtn = null
      }
      params.alarmLevel = null
      this.getAlarmLevelStatistics(params)
    },
    handleGetAttackEventList(params) {
      if (!params.riskLevel) {
        this.currentBtn = null
      }
      params.riskLevel = null
      this.getAttackAlarmLevelStatistics(params)
    },
    handleGetHoneyEventList(params) {
      if (!params.alarmLevel) {
        this.currentBtn = null
      }
      params.alarmLevel = null
      this.getHoneypotAlarmLevelStatistics(params)
    },
    handleGetApiEventList(params) {
      // 构建统计接口的查询参数，包含所有查询条件
      const statisticsParams = {
        params: {
          beginTime: params.params ? params.params.beginTime : undefined,
          endTime: params.params ? params.params.endTime : undefined
        },
        deviceConfigId: params.deviceConfigId
      }
      // 传递所有查询条件给统计接口
      if (params.riskAssets) {
        statisticsParams.riskAssets = params.riskAssets
      }

      if (params.handleState !== undefined && params.handleState !== null) {
        statisticsParams.handleState = params.handleState
      }

      params.riskType = null
      this.getFlowRiskAssetsStatistics(statisticsParams)
    },
    handleGetIpFilterList(params) {
      this.getFilterLogStatistic(params)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../assets/styles/tabs.scss";
@font-face {
  font-family: electronicFont;
  src: url(../../../assets/fonts/DS-DIGI.ttf);
}

.alert-event-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .head-card-box {
    width: 100%;
    height: 109px;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 8px;
    .head-card-item {
      flex: 1;
      &:nth-last-child(-n+2) {
        width: 17%;
        flex: none;
      }
    }
  }
  .box-content {
    height: calc(100% - 115px);
  }
}

.head-card{
  //height: 119px;
  background-color: #FFFFFF;
  padding: 8px 10px;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  .head-card-title{
    color: #242424;
    font-size: 14px;
    font-weight: 700;
  }
  .head-card-btn-box{
    flex: 1;
    margin-top: 5px;
    margin-bottom: 5px;
    .head-card-btn{
      display: flex;
      align-items: center;
      cursor: pointer;
      .btn-icon{
        width: 35%;
        text-align: center;
        .el-image{
          width: 24px;
          height: 24px;
        }
      }
      .btn-icon1{
        width: 35px;
        text-align: center;
        .el-image{
          width: 24px;
          height: 24px;
        }
      }
      .btn-content{
        height: 40px;
        padding-left: 5px;
        display: flex;
        flex-direction: column;
        position: relative;
        flex: 1;
        .btn-content-value{
          font-size: 18px;
          font-weight: 700;
          font-family: electronicFont;
        }
        .btn-content-label{
          position: absolute;
          bottom: 0;
          font-weight: 400;
          white-space: nowrap;
        }
      }
    }

    .head-btn-active{
      background-color: #f3f8fe;
    }
  }
}
.active{
  border: 1px solid #4382FD;
}
</style>
